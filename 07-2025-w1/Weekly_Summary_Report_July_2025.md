# Weekly Summary Report - July 2025
**Reporting Period:** Week 1 July (July 1-7, 2025)
**Projects:** Telkomsel SmartPay & DIGIHUB
**Compiled from:** PMC SmartPay Update (July 3, 2025) & DIGIHUB Daily Report (July 4, 2025)

---

## Executive Summary

This consolidated report summarizes the status of two major Telkomsel projects during the first week of July 2025. Both projects are in critical development and testing phases with multiple milestones approaching.

### Key Highlights:
- **SmartPay**: Sprint 2 testing phase with regression, performance testing, and security testing in progress
- **DIGIHUB**: Sprint 11 development phase (Day 6 of development)
- **Critical Milestones**: SmartPay G3-Sprint 2 (July 21) and DIGIHUB RFS (August 28-29)
- **Security Assessment**: Vulnerability assessments completed for SmartPay infrastructure
- **Development Progress**: Both projects maintaining on-track status

---

## 📊 Project Status Overview

| Project | Current Phase | Status | Key Milestone | Risk Level |
|---------|---------------|--------|---------------|------------|
| **SmartPay** | Sprint 2 Testing | 🟢 On Track | G3-Sprint 2 (July 21) | 🟡 Medium |
| **DIGIHUB** | Sprint 11 Development | 🟢 On Track | RFS (August 28-29) | 🟡 Medium |

---

## 🚀 SmartPay Project Update

### Current Status (July 3, 2025)
- **Phase**: Sprint 2 Testing & Sprint 3 Preparation
- **Overall Status**: 🟢 In Progress (On-Track)
- **Current Activities**: Regression Testing, Performance Testing, Security Testing
- **Next Major Milestone**: G3-Sprint 2 (July 21, 2025)

### Sprint 2 Testing Progress

**Regression Testing:**
- **Period**: June 26 - July 2, 2025
- **Status**: ✅ Completed
- **Focus**: Validation of completed features and bug fixes

**Performance Testing (NFT-PT):**
- **Period**: June 26 - July 9, 2025
- **Status**: 🔄 In Progress
- **Focus**: System performance validation under load

**Security Testing (NFT-Security):**
- **Period**: July 2 - July 15, 2025
- **Status**: 🔄 In Progress
- **Focus**: Security vulnerability assessment and penetration testing

**Vulnerability Assessment Results (July 1-3, 2025):**
- **Web Application VA**: 6 findings (0 Critical, 0 High, 2 Medium, 4 Informational)
- **Source Code VA**: 17 findings (0 Critical, 0 High, 0 Medium, 17 Informational)
- **Overall Security Status**: Low-medium risk profile, remediation in progress

| Component | Issue Identified | Severity | Status |
|-----------|------------------|----------|--------|
| **SSL/TLS Configuration** | Hostname mismatch (CN=ca) | Medium | Requires remediation |
| **Monitoring Access** | Prometheus metrics unsecured | Medium | Authentication needed |
| **Container Security** | Privileged user execution | Informational | Docker updates required |
| **Security Headers** | Missing HSTS, unsafe CSP | Informational | Configuration updates needed |

### Sprint 2 Feature Completion Status

**Completed Development Features (5/5 Sprint Backlog items):**

1. ✅ Campaign - Automated notification for user registration drop-off
2. ✅ Outstanding debt handling for recycled MSISDN
3. ✅ Save session last page
4. ✅ Share Personal Data-Image File via API
5. ✅ Service Fee Tiering

**Product Backlog Progress (23 total items):**

**High Priority Completed (8 items):**
- ✅ Campaign automated notifications
- ✅ Outstanding debt handling
- ✅ Session management
- ✅ Image file sharing API
- ✅ Service fee tiering
- ✅ Automated reminder system
- ✅ Customer lifecycle enhancement
- ✅ Liveness implementation

**High Priority In Progress (10 items):**
- 🔄 SmartPay Dashboard
- 🔄 API Refund Handling
- 🔄 Callback Repayment
- 🔄 Subscriber blocking for overdue accounts
- 🔄 API for Underwriting, KYC, Fraud Detection
- 🔄 Transaction Journey Enhancement
- 🔄 User token negative case handling
- 🔄 API Limit requests
- 🔄 MyTsel tracker/counter integration
- 🔄 Campaign banner parameters

**Not Started (5 items):**
- 📅 Dukcapil Improvement
- 📅 Installment Use Case
- 📅 Cash Loan Use Case
- 📅 Request API Limit
- 📅 Various enhancement features

### Amartha Integration Progress

**Product Backlog (Amartha Side - 10 items):**

**Completed & Ready (4 items):**
- ✅ Service Fee Tiering - Done, waiting for Telkomsel development
- ✅ API Refund Handling - Done, waiting for Telkomsel development  
- ✅ Callback Repayment - Done, waiting for Telkomsel development
- ✅ API Onboarding - Done, waiting for Telkomsel development

**In Progress (6 items):**
- 🔄 Dukcapil validation (ETA: Release June 11, 2025)
- 🔄 Liveness - Waiting for API Onboarding
- 🔄 OCR Improvement - Need discussion with Telkomsel
- 🔄 Face Match - Waiting for API Onboarding
- 🔄 Image Anti Tampering - Waiting for API Onboarding
- 🔄 Underwriting - Waiting for API Onboarding

**Sprint Backlog (Amartha - 5 items):**
- 🔄 Event tracker repayment
- 🔄 Dukcapil validation
- 🔄 Payment option reorder
- 🔄 Tech debt billing
- 🔄 Dana & Gopay improvement

### Upcoming Milestones
- **G3-Sprint 2**: July 21, 2025
- **RFS**: July 24, 2025
- **FUT**: August 11, 2025
- **RFC**: TBD

---

## 🔧 DIGIHUB Project Update

### Current Status (July 4, 2025)
- **Phase**: Sprint 11 Development (Day 6)
- **Overall Status**: 🟢 In Progress (On-Track)
- **Sprint Planning**: ✅ Completed (June 24, 2025)
- **Development Progress**: Early development phase, 0% completion as of July 3

### Sprint 11 Detailed Schedule
**Duration:** June 25 - August 29, 2025

| Phase | Activity | Start Date | End Date | Duration | Status |
|-------|----------|------------|----------|----------|--------|
| **Planning** | Sprint Planning 11 | 2025-06-24 | 2025-06-24 | 1 day | ✅ Completed |
| **Development** | Development Phase | 2025-06-25 | 2025-07-18 | 24 days | 🔄 In Progress (Day 6) |
| **Development** | UI/UX Checking | 2025-07-21 | 2025-07-22 | 2 days | 📅 Not Started |
| **Testing** | SIT | 2025-07-23 | 2025-07-29 | 7 days | 📅 Not Started |
| **Testing** | UAT & Regression | 2025-07-30 | 2025-08-07 | 9 days | 📅 Not Started |
| **Testing** | NFT | 2025-08-08 | 2025-08-21 | 14 days | 📅 Not Started |
| **Testing** | VA & Pentest | 2025-08-08 | 2025-08-22 | 15 days | 📅 Not Started |
| **Testing** | Remediation & Retest | 2025-08-25 | 2025-08-27 | 3 days | 📅 Not Started |
| **Deployment** | RFS | 2025-08-28 | 2025-08-29 | 2 days | 📅 Not Started |

### Sprint 11 Features & Assessment Status

**Completed Assessments:**
- ✅ **Settlement Automation Assessment** (Completed June 2, 2025)
  - Integration discussions needed with SalesForce team
  - Integration discussions needed with Privy team for signature document settlement
- ✅ **2FA Authenticator for Storefront** (Completed June 9, 2025)
- ✅ **Approval Plan Mandays from IT Tower** (Completed June 20, 2025)

**Development Baseline Planning:**
- **Total Story Points**: 61 points (100% target by July 18)
- **Current Progress**: 0% (as of July 3, 2025)
- **Planned Milestones**:
  - July 8: 31% completion (19 story points)
  - July 11: 52% completion (31.5 story points)
  - July 15: 79% completion (48 story points)
  - July 18: 100% completion (61 story points)

### DevOps Activities & Infrastructure

**Ongoing DevOps Tasks:**

1. **Integration to USSD, MNV & TID** (In Progress)
   - **Timeline**: June 18 - July 11, 2025
   - **Status**: Requires discussion with USSD team
   - **Dependencies**: IFA USSD, MNV & TID documentation needed

2. **API Push Method Enhancement** (In Progress)
   - **Timeline**: June 9 - July 4, 2025
   - **Status**: In progress assessment phase

3. **Toggle Filtering Settlement Enhancement** (Not Started)
   - **Priority**: Medium
   - **Timeline**: TBD

**Completed DevOps Items:**
- ✅ Enhancement Feeding Data from Metering to SELVI (Completed)
- ✅ Resolved mismatch data settlement Metering (Completed)

### Key Dependencies & Prerequisites

**Pre-requisites Status:**
- **Privy Integration**: IFA required for multi-layer approval BAK
- **Documentation**: HLD, LLD, API Contract, SRS documentation
- **Adobe Integration**: Configuration pending

### Upcoming DIGIHUB Milestones
- **Development Completion**: July 18, 2025
- **UI/UX Checking**: July 21-22, 2025
- **SIT Phase**: July 23-29, 2025
- **UAT & Regression**: July 30 - August 7, 2025
- **RFS**: August 28-29, 2025

---

## 🏗️ SmartPay Klop Infrastructure Analysis

### Infrastructure Migration Overview

**Project:** SmartPay Klop Infrastructure Migration
**Objective:** Complete post-migration optimization and security hardening
**Status:** ✅ Migration Completed - Production environment operational since June 2025
**Current Focus:** Security vulnerability remediation and performance optimization

### Post-Migration Infrastructure Status
**Production Environment Performance (July 2025):**

- **Application Status**: ✅ Fully operational on production cluster
- **System Stability**: Stable performance since migration completion
- **Resource Utilization**: Optimized allocation post-migration
- **Customer Load**: Successfully supporting current user base with improved capacity
- **Infrastructure Benefits Realized**:
  - Dedicated resources (no longer shared with Cascade, POTLOC, Digihub)
  - Enhanced performance and scalability
  - Improved monitoring and alerting capabilities

### Infrastructure Optimization Initiatives

#### 1. **Security Hardening Phase**
**Timeline:** July 8-15, 2025
**Priority:** High (Pre-G3-Sprint 2)

- **SSL Certificate Management**: Update certificates with proper hostname matching
- **Access Control Implementation**: Secure monitoring endpoints with authentication
- **Container Security**: Migrate Docker configurations to non-root execution
- **Security Headers**: Implement HSTS and secure Content Security Policy

#### 2. **Performance Monitoring Enhancement**
**Timeline:** July 8-21, 2025

- **Metrics Security**: Maintain Prometheus monitoring while implementing access controls
- **Reverse Proxy Optimization**: Fine-tune Envoy Proxy configurations
- **Application Performance**: Monitor response times and resource utilization

### Infrastructure Dependencies & Integration Points

**Current Production Integrations:**
- **Amartha API**: https://api-uat.amartha.id (UAT environment integration)
- **Google Analytics**: Tag Manager integration for user tracking
- **MyTsel Platform**: Cross-platform functionality support
- **Internal Services**: Integration with Telkomsel internal systems

### Infrastructure Risk Assessment

**Medium Priority Infrastructure Risks:**

1. **SSL Certificate Trust Issues**
   - **Impact**: User browser warnings, potential trust degradation
   - **Timeline**: Immediate resolution required (by July 15)
   - **Mitigation**: Certificate update with proper CA signing

2. **Monitoring Security Exposure**
   - **Impact**: Potential system information disclosure
   - **Timeline**: Resolution by July 10
   - **Mitigation**: Implement authentication for metrics endpoints

3. **Container Security Posture**
   - **Impact**: Potential privilege escalation vulnerabilities
   - **Timeline**: Resolution by July 21 (G3-Sprint 2)
   - **Mitigation**: Update all Docker configurations to non-root execution

### Infrastructure Impact Assessment

**Migration Success Metrics (July 2025):**

**Achieved Benefits:**
- **Resource Liberation**: ✅ Freed up shared resources for other projects (Cascade, POTLOC, Digihub)
- **Performance Improvement**: ✅ Dedicated infrastructure delivering stable performance
- **Scalability**: ✅ Infrastructure now capable of supporting planned growth
- **Operational Stability**: ✅ Zero migration-related incidents since deployment

**Current Week Focus (July 1-7, 2025):**
- Security vulnerability remediation (Sprint 2 NFT-Security testing)
- SSL certificate configuration updates
- Container security hardening
- Performance monitoring optimization

### Infrastructure Readiness for RFS

**RFS Preparation Checklist (Target: July 24, 2025):**

- [ ] SSL certificate remediation completed
- [ ] Security vulnerability assessment passed
- [ ] Performance benchmarks validated
- [ ] Monitoring and alerting systems secured
- [ ] Container security configurations updated
- [ ] Integration endpoints tested and validated

**Infrastructure Capacity Validation:**
- ✅ Production environment proven stable post-migration
- ✅ Resource allocation adequate for current user load
- ✅ Scalability framework in place for future growth
- 🔄 Security posture enhancement in progress (Sprint 2 testing)

---

## ⚠️ Critical Issues & RAID Analysis

### High Priority Issues

#### 1. **Operational Issues (Klop Platform)**

**OCR Improvement Validation:**
- **Issue**: Low OCR accuracy causing corrupted data validation
- **Impact**: Invalid data being sent without proper validation
- **Status**: In Progress
- **Timeline**: June 17 - July 8, 2025
- **Severity**: Medium
- **PIC**: Avian/Syakir

**Intermittent 410 Error:**
- **Issue**: System bug causing intermittent 410 errors
- **Action**: End-to-end process tracking and data flow auditing
- **Status**: In Progress
- **Timeline**: June 16 - July 2, 2025
- **Severity**: High
- **PIC**: Avian/Syakir

**Gojek Redirect URL Error:**
- **Issue**: Klop doesn't handle URL redirection properly
- **Action**: Joint investigation with MyTsel and Amartha required
- **Status**: In Progress
- **Timeline**: June 16 - ongoing
- **Severity**: High
- **PIC**: Avian/Syakir

#### 2. **Data Management Issues**

**Automation Data Funnel:**
- **Issue**: Format differences and delimiter additions
- **Action**: Alignment with BI team, filename adjustments
- **Status**: In Progress
- **Timeline**: July 2-4, 2025
- **Severity**: High
- **PIC**: Avian/Syakir

#### 3. **Sprint 2 Security Testing Issues**

**SSL Certificate Configuration:**
- **Issue**: SSL certificate hostname mismatch (CN=ca vs actual hostname)
- **Severity**: Medium
- **Impact**: Browser certificate warnings for users
- **Status**: Identified in VA, remediation required
- **Timeline**: Resolution by July 15 (before G3-Sprint 2)

**Prometheus Metrics Security:**
- **Issue**: Unrestricted access to monitoring metrics endpoint
- **Severity**: Medium (CVSS: 5.3)
- **Impact**: Potential system information disclosure
- **Status**: Identified in VA, authentication implementation needed
- **Timeline**: Resolution by July 10

**Container Security Configuration:**
- **Issue**: Docker configurations using privileged users and recursive copying
- **Severity**: Informational (17 findings)
- **Impact**: Potential security vulnerabilities in deployment
- **Status**: Source code VA completed, updates needed
- **Timeline**: Resolution by July 21 (G3-Sprint 2)

#### 4. **Integration Dependencies**

**Refresh Token Solution:**
- **Issue**: IT EA team concerns regarding refresh token implementation
- **Status**: On Hold
- **Impact**: Token management architecture needs revision
- **Next Steps**: Re-visit solution and share updated plan

**Manual Image File Sharing:**
- **Issue**: Compliance requirements from ICT for image file sharing
- **Status**: ✅ Completed (July 2, 2025)
- **Resolution**: Standard change approach implemented

**API Image File Sharing:**
- **Issue**: Mechanism for sharing image files via API
- **Status**: In Progress
- **Timeline**: TBD
- **PIC**: Dodi (OPS)

### Medium Priority Issues

#### SmartPay:
1. **Dashboard Design**: SmartPay dashboard still in design phase
2. **API Integration Dependencies**: Multiple APIs waiting for Telkomsel development
3. **Testing Resource Coordination**: Managing concurrent testing phases

#### DIGIHUB:
1. **Integration Dependencies**: USSD, MNV & TID integration discussions pending
2. **Documentation Requirements**: IFA documentation needed for multiple integrations
3. **Development Timeline**: Early stage with 0% completion, need to accelerate to meet July 18 target

---

## 📈 Performance Metrics & User Analytics

### User Registration Analysis (June 1 - July 2, 2025)

**Registration Trends:**
- **June 2025**: 1,838 total registrations
  - Registered: 1,477 users
  - Rejected: 338 users
- **July 2025 (1-2 days)**: 22 total registrations
  - Registered: 16 users  
  - Rejected: 6 users

**Historical Context:**
- **April 2025 Peak**: 32,838 registrations (29,907 registered, 2,924 rejected)
- **May 2025 Drop**: 9,103 registrations (-72.4% decline)
- **Cause**: Deliberate reduction in whitelisted MSISDNs

**Key Insights:**
- Registration decline was expected due to whitelist reduction strategy
- Approval rate remains high: ~89% approval rate maintained
- July trend needs continued monitoring for recovery patterns

---

## 📅 Upcoming Critical Milestones

### July 2025
| Date | Project | Milestone | Status |
|------|---------|-----------|--------|
| July 8 | SmartPay | OCR Improvement Completion | 🔄 In Progress |
| July 9 | SmartPay | NFT-PT Completion | 🔄 In Progress |
| July 15 | SmartPay | NFT-Security Completion | 📅 Scheduled |
| July 21 | SmartPay | G3-Sprint 2 | 🎯 Critical |
| July 24 | SmartPay | RFS | 🎯 Critical |

### August 2025
| Date | Project | Milestone | Status |
|------|---------|-----------|--------|
| Aug 1 | SmartPay | G2-Sprint 3 | 📅 Scheduled |
| Aug 11 | SmartPay | FUT | 📅 Scheduled |

---

## 🎯 Action Items & Recommendations

### Immediate Actions (Next Week - July 8-14)

**SmartPay Testing:**
- [ ] Complete NFT-PT testing phase (by July 9)
- [ ] Continue NFT-Security testing
- [ ] Resolve OCR improvement validation issues
- [ ] Address intermittent 410 errors and Gojek redirect issues

**Sprint 2 Security Testing Remediation (High Priority):**
- [ ] Fix SSL certificate hostname mismatch identified in VA testing
- [ ] Implement authentication for Prometheus metrics endpoint
- [ ] Update Content Security Policy to remove unsafe directives
- [ ] Enable HTTP Strict Transport Security (HSTS) headers
- [ ] Update Docker configurations to use non-root users
- [ ] Complete security remediation before G3-Sprint 2 milestone

**Integration & Development:**
- [ ] Finalize API integrations waiting for Telkomsel development
- [ ] Complete automation data funnel adjustments
- [ ] Progress SmartPay Dashboard design
- [ ] Update Dockerfiles to implement non-root user configurations

**Preparation for G3-Sprint 2:**
- [ ] Prepare documentation and deliverables for July 21 milestone
- [ ] Coordinate with all stakeholders for milestone review
- [ ] Ensure all testing phases are on track for completion
- [ ] Complete security vulnerability remediation before milestone

**DIGIHUB Development (Critical):**
- [ ] Accelerate Sprint 11 development to achieve planned milestones
- [ ] Complete USSD, MNV & TID integration discussions
- [ ] Finalize IFA documentation requirements
- [ ] Progress towards 31% completion target by July 8
- [ ] Coordinate with SalesForce and Privy teams for settlement automation

### Strategic Recommendations

#### 1. **Testing Coordination**
- Implement parallel testing strategies to optimize timeline
- Establish clear communication channels between testing teams
- Create contingency plans for testing delays

#### 2. **Issue Resolution Priority**
- Focus on high-severity operational issues (410 errors, Gojek redirect)
- Accelerate OCR improvement to enhance data quality
- Establish escalation procedures for critical bugs

#### 3. **Integration Management**
- Coordinate closely with Amartha team on API dependencies
- Establish clear timelines for Telkomsel development dependencies
- Create integration testing schedules

#### 4. **Performance Monitoring**
- Continue monitoring user registration trends
- Implement automated alerting for performance degradation
- Establish baseline metrics for post-RFS monitoring

#### 5. **Infrastructure & Security Enhancement**
- Establish security-first development practices for container configurations
- Implement regular vulnerability assessments as part of CI/CD pipeline
- Create security checklist for pre-production deployments
- Coordinate security remediation with testing phases to avoid delays
- Develop infrastructure monitoring and alerting for production readiness
- Establish infrastructure change management procedures for RFS preparation

---

## 🔮 Looking Ahead

### Next Week Priorities (July 8-14)

**SmartPay:**
1. **Complete NFT-PT testing phase**
2. **Progress NFT-Security testing**
3. **Resolve critical operational issues**
4. **Complete infrastructure security remediation**
5. **Prepare G3-Sprint 2 documentation**
6. **Validate infrastructure readiness for RFS**

**DIGIHUB:**
7. **Achieve 31-52% development completion (19-31.5 story points)**
8. **Finalize USSD, MNV & TID integration discussions**
9. **Complete API Push Method assessment**
10. **Coordinate Sprint 3 planning activities**

### Month-End Targets (July 31)

**SmartPay:**
1. **Successful G3-Sprint 2 milestone achievement**
2. **RFS completion and deployment**
3. **All critical operational issues resolved**
4. **All medium-priority security vulnerabilities remediated**
5. **Sprint 3 planning and initiation**
6. **User registration trend stabilization**
7. **Enhanced security posture with updated container configurations**

**DIGIHUB:**
8. **Complete Sprint 11 development phase (100% by July 18)**
9. **Successful UI/UX checking completion**
10. **SIT phase initiation and progress**
11. **All integration dependencies resolved**

---

*Report compiled by: Project Management Office*  
*Sources: PMC SmartPay Update (July 3, 2025)*  
*Next update: July 10, 2025*
