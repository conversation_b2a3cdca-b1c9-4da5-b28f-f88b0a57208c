# Weekly Summary Report - July 2025
**Reporting Period:** Week 1 July (July 1-7, 2025)  
**Projects:** Telkomsel SmartPay  
**Compiled from:** PMC SmartPay Update (July 3, 2025)

---

## Executive Summary

This report summarizes the status of the Telkomsel SmartPay project during the first week of July 2025. The project is in a critical phase with Sprint 2 nearing completion and multiple testing activities in progress, preparing for key milestones including G3-Sprint 2 and RFS.

### Key Highlights:
- **SmartPay**: Sprint 2 testing phase with regression, performance testing, and security testing in progress
- **Critical Milestone**: G3-Sprint 2 scheduled for July 21, 2025
- **User Registration**: Continued monitoring of registration trends after May decline
- **Testing Progress**: Multiple concurrent testing phases (Regression, NFT-PT, NFT-Security)

---

## 📊 Project Status Overview

| Project | Current Phase | Status | Key Milestone | Risk Level |
|---------|---------------|--------|---------------|------------|
| **SmartPay** | Sprint 2 Testing | 🟢 On Track | G3-Sprint 2 (July 21) | 🟡 Medium |

---

## 🚀 SmartPay Project Update

### Current Status (July 3, 2025)
- **Phase**: Sprint 2 Testing & Sprint 3 Preparation
- **Overall Status**: 🟢 In Progress (On-Track)
- **Current Activities**: Regression Testing, Performance Testing, Security Testing
- **Next Major Milestone**: G3-Sprint 2 (July 21, 2025)

### Sprint 2 Testing Progress

**Regression Testing:**
- **Period**: June 26 - July 2, 2025
- **Status**: ✅ Completed
- **Focus**: Validation of completed features and bug fixes

**Performance Testing (NFT-PT):**
- **Period**: June 26 - July 9, 2025
- **Status**: 🔄 In Progress
- **Focus**: System performance validation under load

**Security Testing (NFT-Security):**
- **Period**: July 2 - July 15, 2025
- **Status**: 🔄 In Progress
- **Focus**: Security vulnerability assessment and penetration testing

### Sprint 2 Feature Completion Status

**Completed Development Features (5/5 Sprint Backlog items):**

1. ✅ Campaign - Automated notification for user registration drop-off
2. ✅ Outstanding debt handling for recycled MSISDN
3. ✅ Save session last page
4. ✅ Share Personal Data-Image File via API
5. ✅ Service Fee Tiering

**Product Backlog Progress (23 total items):**

**High Priority Completed (8 items):**
- ✅ Campaign automated notifications
- ✅ Outstanding debt handling
- ✅ Session management
- ✅ Image file sharing API
- ✅ Service fee tiering
- ✅ Automated reminder system
- ✅ Customer lifecycle enhancement
- ✅ Liveness implementation

**High Priority In Progress (10 items):**
- 🔄 SmartPay Dashboard
- 🔄 API Refund Handling
- 🔄 Callback Repayment
- 🔄 Subscriber blocking for overdue accounts
- 🔄 API for Underwriting, KYC, Fraud Detection
- 🔄 Transaction Journey Enhancement
- 🔄 User token negative case handling
- 🔄 API Limit requests
- 🔄 MyTsel tracker/counter integration
- 🔄 Campaign banner parameters

**Not Started (5 items):**
- 📅 Dukcapil Improvement
- 📅 Installment Use Case
- 📅 Cash Loan Use Case
- 📅 Request API Limit
- 📅 Various enhancement features

### Amartha Integration Progress

**Product Backlog (Amartha Side - 10 items):**

**Completed & Ready (4 items):**
- ✅ Service Fee Tiering - Done, waiting for Telkomsel development
- ✅ API Refund Handling - Done, waiting for Telkomsel development  
- ✅ Callback Repayment - Done, waiting for Telkomsel development
- ✅ API Onboarding - Done, waiting for Telkomsel development

**In Progress (6 items):**
- 🔄 Dukcapil validation (ETA: Release June 11, 2025)
- 🔄 Liveness - Waiting for API Onboarding
- 🔄 OCR Improvement - Need discussion with Telkomsel
- 🔄 Face Match - Waiting for API Onboarding
- 🔄 Image Anti Tampering - Waiting for API Onboarding
- 🔄 Underwriting - Waiting for API Onboarding

**Sprint Backlog (Amartha - 5 items):**
- 🔄 Event tracker repayment
- 🔄 Dukcapil validation
- 🔄 Payment option reorder
- 🔄 Tech debt billing
- 🔄 Dana & Gopay improvement

### Upcoming Milestones
- **G3-Sprint 2**: July 21, 2025
- **RFS**: July 24, 2025
- **FUT**: August 11, 2025
- **RFC**: TBD

---

## 🔒 Security Assessment Results

### Vulnerability Assessment Summary (July 1-3, 2025)

Two comprehensive security assessments were conducted during the first week of July 2025:

#### Web Application Security Assessment (July 3, 2025)
**Target**: Klop Loan Marketplace Web Application (https://*************/)
**Scan Results**:
- **Critical**: 0 vulnerabilities
- **High**: 0 vulnerabilities
- **Medium**: 2 vulnerabilities
- **Low**: 0 vulnerabilities
- **Informational**: 4 findings
- **Total**: 6 findings

#### Source Code Security Assessment (July 1, 2025)
**Target**: Loan Marketplace Source Code (Multiple Dockerfiles)
**Scan Results**:
- **Critical**: 0 vulnerabilities
- **High**: 0 vulnerabilities
- **Medium**: 0 vulnerabilities
- **Low**: 0 vulnerabilities
- **Informational**: 17 findings
- **Total**: 17 findings

### Medium Priority Security Issues (Web Application)

#### 1. **SSL Certificate Name Hostname Mismatch**
- **Severity**: Medium
- **Issue**: SSL certificate common name (CN=ca) doesn't match the hostname
- **Impact**: Certificate validation warnings for users
- **Recommendation**: Update SSL certificate with proper hostname matching

#### 2. **Unrestricted Access to Prometheus Metrics**
- **Severity**: Medium (CVSS: 5.3)
- **Issue**: Prometheus metrics endpoint accessible without authentication
- **Impact**: Potential information disclosure about system performance
- **Recommendation**: Implement authentication for metrics endpoint

### Informational Security Findings

#### Web Application (4 findings):
1. **Reverse Proxy Detected**: Envoy Proxy identified - informational only
2. **Unsafe Content Security Policy**: Uses 'unsafe-eval' and 'unsafe-inline' directives
3. **Missing HSTS Policy**: HTTP Strict Transport Security not enabled
4. **SSL Untrusted Root Certificate**: Self-signed certificate in use

#### Source Code (17 findings):
1. **Docker Security Issues**: Multiple Dockerfiles with security-sensitive configurations
   - Recursive copying of context directories (8 instances)
   - Running containers as privileged user (9 instances)

### Security Recommendations

#### Immediate Actions:
- [ ] Fix SSL certificate hostname mismatch
- [ ] Secure Prometheus metrics endpoint with authentication
- [ ] Implement proper Content Security Policy without unsafe directives
- [ ] Enable HTTP Strict Transport Security (HSTS)

#### Development Security Improvements:
- [ ] Update Dockerfiles to use non-root users
- [ ] Implement specific file copying instead of recursive directory copying
- [ ] Review and update container security configurations

---

## ⚠️ Critical Issues & RAID Analysis

### High Priority Issues

#### 1. **Operational Issues (Klop Platform)**

**OCR Improvement Validation:**
- **Issue**: Low OCR accuracy causing corrupted data validation
- **Impact**: Invalid data being sent without proper validation
- **Status**: In Progress
- **Timeline**: June 17 - July 8, 2025
- **Severity**: Medium
- **PIC**: Avian/Syakir

**Intermittent 410 Error:**
- **Issue**: System bug causing intermittent 410 errors
- **Action**: End-to-end process tracking and data flow auditing
- **Status**: In Progress
- **Timeline**: June 16 - July 2, 2025
- **Severity**: High
- **PIC**: Avian/Syakir

**Gojek Redirect URL Error:**
- **Issue**: Klop doesn't handle URL redirection properly
- **Action**: Joint investigation with MyTsel and Amartha required
- **Status**: In Progress
- **Timeline**: June 16 - ongoing
- **Severity**: High
- **PIC**: Avian/Syakir

#### 2. **Data Management Issues**

**Automation Data Funnel:**
- **Issue**: Format differences and delimiter additions
- **Action**: Alignment with BI team, filename adjustments
- **Status**: In Progress
- **Timeline**: July 2-4, 2025
- **Severity**: High
- **PIC**: Avian/Syakir

#### 3. **Integration Dependencies**

**Refresh Token Solution:**
- **Issue**: IT EA team concerns regarding refresh token implementation
- **Status**: On Hold
- **Impact**: Token management architecture needs revision
- **Next Steps**: Re-visit solution and share updated plan

**Manual Image File Sharing:**
- **Issue**: Compliance requirements from ICT for image file sharing
- **Status**: ✅ Completed (July 2, 2025)
- **Resolution**: Standard change approach implemented

**API Image File Sharing:**
- **Issue**: Mechanism for sharing image files via API
- **Status**: In Progress
- **Timeline**: TBD
- **PIC**: Dodi (OPS)

### Medium Priority Issues
1. **Dashboard Design**: SmartPay dashboard still in design phase
2. **API Integration Dependencies**: Multiple APIs waiting for Telkomsel development
3. **Testing Resource Coordination**: Managing concurrent testing phases

---

## 📈 Performance Metrics & User Analytics

### User Registration Analysis (June 1 - July 2, 2025)

**Registration Trends:**
- **June 2025**: 1,838 total registrations
  - Registered: 1,477 users
  - Rejected: 338 users
- **July 2025 (1-2 days)**: 22 total registrations
  - Registered: 16 users  
  - Rejected: 6 users

**Historical Context:**
- **April 2025 Peak**: 32,838 registrations (29,907 registered, 2,924 rejected)
- **May 2025 Drop**: 9,103 registrations (-72.4% decline)
- **Cause**: Deliberate reduction in whitelisted MSISDNs

**Key Insights:**
- Registration decline was expected due to whitelist reduction strategy
- Approval rate remains high: ~89% approval rate maintained
- July trend needs continued monitoring for recovery patterns

---

## 📅 Upcoming Critical Milestones

### July 2025
| Date | Project | Milestone | Status |
|------|---------|-----------|--------|
| July 8 | SmartPay | OCR Improvement Completion | 🔄 In Progress |
| July 9 | SmartPay | NFT-PT Completion | 🔄 In Progress |
| July 15 | SmartPay | NFT-Security Completion | 📅 Scheduled |
| July 21 | SmartPay | G3-Sprint 2 | 🎯 Critical |
| July 24 | SmartPay | RFS | 🎯 Critical |

### August 2025
| Date | Project | Milestone | Status |
|------|---------|-----------|--------|
| Aug 1 | SmartPay | G2-Sprint 3 | 📅 Scheduled |
| Aug 11 | SmartPay | FUT | 📅 Scheduled |

---

## 🎯 Action Items & Recommendations

### Immediate Actions (Next Week - July 8-14)

**SmartPay Testing:**
- [ ] Complete NFT-PT testing phase (by July 9)
- [ ] Continue NFT-Security testing
- [ ] Resolve OCR improvement validation issues
- [ ] Address intermittent 410 errors and Gojek redirect issues

**Security Remediation (High Priority):**
- [ ] Fix SSL certificate hostname mismatch for production environment
- [ ] Implement authentication for Prometheus metrics endpoint
- [ ] Update Content Security Policy to remove unsafe directives
- [ ] Enable HTTP Strict Transport Security (HSTS) headers

**Integration & Development:**
- [ ] Finalize API integrations waiting for Telkomsel development
- [ ] Complete automation data funnel adjustments
- [ ] Progress SmartPay Dashboard design
- [ ] Update Dockerfiles to implement non-root user configurations

**Preparation for G3-Sprint 2:**
- [ ] Prepare documentation and deliverables for July 21 milestone
- [ ] Coordinate with all stakeholders for milestone review
- [ ] Ensure all testing phases are on track for completion
- [ ] Complete security vulnerability remediation before milestone

### Strategic Recommendations

#### 1. **Testing Coordination**
- Implement parallel testing strategies to optimize timeline
- Establish clear communication channels between testing teams
- Create contingency plans for testing delays

#### 2. **Issue Resolution Priority**
- Focus on high-severity operational issues (410 errors, Gojek redirect)
- Accelerate OCR improvement to enhance data quality
- Establish escalation procedures for critical bugs

#### 3. **Integration Management**
- Coordinate closely with Amartha team on API dependencies
- Establish clear timelines for Telkomsel development dependencies
- Create integration testing schedules

#### 4. **Performance Monitoring**
- Continue monitoring user registration trends
- Implement automated alerting for performance degradation
- Establish baseline metrics for post-RFS monitoring

#### 5. **Security Enhancement**
- Establish security-first development practices for container configurations
- Implement regular vulnerability assessments as part of CI/CD pipeline
- Create security checklist for pre-production deployments
- Coordinate security remediation with testing phases to avoid delays

---

## 🔮 Looking Ahead

### Next Week Priorities (July 8-14)
1. **Complete NFT-PT testing phase**
2. **Progress NFT-Security testing**
3. **Resolve critical operational issues**
4. **Prepare G3-Sprint 2 documentation**
5. **Coordinate Sprint 3 planning activities**

### Month-End Targets (July 31)
1. **Successful G3-Sprint 2 milestone achievement**
2. **RFS completion and deployment**
3. **All critical operational issues resolved**
4. **Sprint 3 planning and initiation**
5. **User registration trend stabilization**

---

*Report compiled by: Project Management Office*  
*Sources: PMC SmartPay Update (July 3, 2025)*  
*Next update: July 10, 2025*
