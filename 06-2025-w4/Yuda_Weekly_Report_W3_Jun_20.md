# Yuda - Weekly Report (W3 Jun - 20 Jun)

## DIGIHUB Project Status

### Sprint 10 Overview
- **Status**: Grooming, Assessment, Development
- **API Active Status**: In progress
- **API Compound**: In development

### Testing Schedule
- **SIT**: Complete all test executions by April 15, 2025
- **UAT**: Finished all planned test executions on day 2, ahead of original schedule (April 16-23, 2025)
- **Regression**: April 23 - complete all test executions by April 22, 2025
- **NFT**: Progress - April 24 - May 6
- **Pentest**: April 25 - May 7
- **RFS**: May 22

### Sprint 11 (Drop 1): Settlement Automation & 2FA

#### Team Allocation & Workload
The average total estimated days per assignee across the sprint: **13.92 days**

- **Backend (Hanapi)**: 16.25 days (16.76% above average)
- **Frontend (Fanny)**: 13.75 days (1.20% below average)
- **Backend (Fatur)**: 11.75 days (15.57% below average)

#### Key Activities Schedule

**Sprint Planning**:
- Sprint 11 & 12: Scheduled for June 18th and July 7th, 2025 (one day each)

**Development Phase**:
- Sprint 11: June 19th to July 4th, 2025
- Sprint 12: July 8th to July 16th, 2025

**Testing and Validation**:
- UI/UX Checking: July 17th-18th, 2025
- SIT (System Integration Testing): July 21st-28th, 2025
- UAT & Regression: July 29th-August 7th, 2025
- NFT (Non-Functional Testing): August 8th-19th, 2025
- VA (Vulnerability Assessment): August 8th-22nd, 2025
- Pentest (Penetration Testing): August 11th-25th, 2025
- Remediation & Retest: August 26th-29th, 2025
- FUT (Final User Testing): September 3rd-17th, 2025

**Deployment/Release**:
- RFS (Ready for Service): September 1st-2nd, 2025
- RFC (Ready for Change): September 18th-19th, 2025

### Gate 2 Documents - Sprint 11 & 12

**Document Preparation Schedule**:
- **SRS** (System Requirements Specification): June 19th-24th, 2025
- **SIT TCs** (System Integration Test Cases): June 19th-July 17th, 2025 (± 70 TCs)
- **UT Document Result & Sign Off**: July 18th-19th, 2025
- **IP/Port Information** for VA & Security Test: August 1st, 2025
- **API List for NFT**: July 16th-17th, 2025

## Operational Activities

### Key Operational Items (Week of June 20th)
- **2FA Implementation on Digihub CMS**: June 18th
- **Assessment Policy on WAF**: In progress
- **Critical Issue Identified**: Business team identified critical anomaly in transaction traffic between Splunk logs and metering database (Thursday, June 26, 2025 09:31)

### Sprint 2 Activities

#### Key Milestones
- **BR Doc**: Complete by May 2
- **LLD & IFA**: Complete by May 23
- **Design Solution**: Complete by May 16
- **G2-Sprint 2**: By June 5
- **G3-Sprint 2**: By July 21

#### Development Timeline
- **Requirement Grooming**: April 21 – May 2
- **Sprint Planning**: May 5 – May 16
- **Development and UT**: May 19 – June 2

#### Key Features in Development
1. **Ultimate Service Fee**: Development of tiering fee structure and automatic reversal
2. **Automated Reminder System**: Implementation of billing and repayment reminders via email, SMS, Push notification (MyTSEL)
3. **Save Session Last Page**: Implementation of session saving for the last page to prevent drop-offs
4. **Customer Lifecycle Enhancement**: Various improvements

#### Testing Schedule
- **SIT**: June 5 – June 11
- **UAT**: June 12 – June 25
- **Regression**: June 26 – July 2
- **NFT-PT**: June 26 – July 9
- **NFT Security**: June 26 – July 9
- **NFT Security Remediation**: July 2 – July 15

#### Release Timeline
- **RFS**: July 24
- **FUT**: July 25 – August 7
- **RFC**: August 11

## Action Items & Priorities

### Immediate Actions
- Monitor and resolve transaction traffic anomaly between Splunk logs and metering database
- Complete 2FA implementation on Digihub CMS
- Implement connection prioritization and circuit breakers

### Medium-term Actions (RFS May 27)
- Develop automated reconciliation process
- Refactor application architecture for better resilience
- Set up connection pool monitoring and alerting

### Long-term Actions (RFS June 11)
- Implement enhanced logging with correlation IDs
- Develop reconciliation reports between Splunk and metering database
- Implement comprehensive end-to-end transaction tracking
- Establish continuous monitoring and improvement process
- Consider database scaling or sharding for improved capacity

## In-Progress Items (15 items)

### Data Management
1. **[RAID] Sync Data Reporting**: Synchronization of Amartha data daily in Minio and status updates from KLOP to DFS Ops
   - **PIC**: Amartha/Klop/DFS ops
   - **Latest Progress**: Amartha informed it will be ready by June 6
   - **Current PIC**: Amartha

2. **[RAID] Data Tracker**: Implementation of data funnel tracker with raw data provision and ingestion to Hadoop
   - **PIC**: Klop/BI
   - **Sample File Preparation**: Klop Team to resend the sample file (with modification and filename) to BI for current development check

---

*Report compiled by: Yuda*  
*Date: Week 3 June (June 20, 2025)*  
*Project: DIGIHUB*
