<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PMC SmartPay Project Update</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .meta {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .toc {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .toc h2 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        .toc a {
            color: #555;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 3px;
            transition: all 0.3s;
        }
        
        .toc a:hover {
            background: #667eea;
            color: white;
        }
        
        h1, h2, h3, h4, h5, h6 {
            margin: 30px 0 15px 0;
            color: #2c3e50;
        }
        
        h1 { font-size: 2.2em; border-bottom: 3px solid #667eea; padding-bottom: 10px; }
        h2 { font-size: 1.8em; border-bottom: 2px solid #ddd; padding-bottom: 8px; }
        h3 { font-size: 1.5em; color: #667eea; }
        h4 { font-size: 1.3em; }
        
        p {
            margin: 15px 0;
            text-align: justify;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background: #f5f5f5;
        }
        
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            color: #e74c3c;
        }
        
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 20px 0;
        }
        
        pre code {
            background: none;
            color: inherit;
            padding: 0;
        }
        
        blockquote {
            border-left: 4px solid #667eea;
            margin: 20px 0;
            padding: 15px 20px;
            background: #f8f9fa;
            font-style: italic;
        }
        
        ul, ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 8px 0;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin: 2px;
        }
        
        .status-completed { background: #d4edda; color: #155724; }
        .status-progress { background: #fff3cd; color: #856404; }
        .status-scheduled { background: #cce5ff; color: #004085; }
        .status-risk { background: #f8d7da; color: #721c24; }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            body { padding: 10px; }
            .content { padding: 20px; }
            .header { padding: 20px; }
            .header h1 { font-size: 2em; }
            table { font-size: 0.9em; }
        }
        
        .print-only { display: none; }
        
        @media print {
            body { background: white; padding: 0; }
            .container { box-shadow: none; }
            .header { background: #667eea !important; }
            .print-only { display: block; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PMC SmartPay Project Update</h1>
            <div class="meta">Generated on June 26, 2025 at 10:36 AM</div>
        </div>
        <div class="content">
            <div class="toc"><h2>📋 Table of Contents</h2><div class="toc">
<ul>
<li><a href="#pmc-smartpay-project-update">PMC SmartPay Project Update</a><ul>
<li><a href="#executive-summary">Executive Summary</a></li>
<li><a href="#table-of-contents">Table of Contents</a></li>
<li><a href="#program-schedule">1. Program Schedule, Milestones &amp; Key Management Highlights</a><ul>
<li><a href="#high-level-project-timeline-2025">High-Level Project Timeline (2025)</a><ul>
<li><a href="#current-status-were-here-week-of-june-26">Current Status: We're here (Week of June 26)</a></li>
</ul>
</li>
<li><a href="#sprint-2-timeline">Sprint 2 Timeline</a><ul>
<li><a href="#key-milestones-completed">Key Milestones Completed:</a></li>
<li><a href="#upcoming-milestones">Upcoming Milestones:</a></li>
</ul>
</li>
<li><a href="#sprint-3-timeline">Sprint 3 Timeline</a></li>
<li><a href="#testing-schedule">Testing Schedule</a><ul>
<li><a href="#current-testing-phase">Current Testing Phase:</a></li>
<li><a href="#future-testing">Future Testing:</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#workstream-update">2. Workstream Update</a><ul>
<li><a href="#smartpay-sprint-2-status">SmartPay Sprint 2 Status</a><ul>
<li><a href="#progress-overview">Progress Overview:</a></li>
</ul>
</li>
<li><a href="#sprint-2-backlog-items">Sprint 2 Backlog Items</a></li>
<li><a href="#key-features-delivered">Key Features Delivered</a><ul>
<li><a href="#1-campaign-automated-notification-system">1. Campaign - Automated Notification System</a></li>
<li><a href="#2-outstanding-debt-handling">2. Outstanding Debt Handling</a></li>
<li><a href="#3-service-fee-tiering">3. Service Fee Tiering</a></li>
<li><a href="#4-automated-reminder-system">4. Automated Reminder System</a></li>
<li><a href="#5-customer-lifecycle-enhancement">5. Customer Lifecycle Enhancement</a></li>
</ul>
</li>
<li><a href="#amartha-product-backlog">Amartha Product Backlog</a></li>
<li><a href="#amartha-sprint-backlog">Amartha Sprint Backlog</a></li>
</ul>
</li>
<li><a href="#raid">3. Risks, Actions, Issues, Dependencies (RAID)</a><ul>
<li><a href="#key-raid-items">Key RAID Items</a></li>
<li><a href="#detailed-raid-analysis">Detailed RAID Analysis</a><ul>
<li><a href="#1-sync-data-reporting-in-progress">1. Sync Data Reporting (In Progress)</a></li>
<li><a href="#2-refresh-token-solution-on-hold">2. Refresh Token Solution (On Hold)</a></li>
<li><a href="#3-manual-image-file-sharing-in-progress">3. Manual Image File Sharing (In Progress)</a></li>
</ul>
</li>
<li><a href="#current-risk-assessment">Current Risk Assessment</a><ul>
<li><a href="#high-priority-risks">🔴 High Priority Risks</a></li>
<li><a href="#medium-priority-issues">🟡 Medium Priority Issues</a></li>
</ul>
</li>
<li><a href="#action-items">Action Items</a><ul>
<li><a href="#immediate-actions-this-week">Immediate Actions (This Week)</a></li>
<li><a href="#short-term-actions-next-2-weeks">Short-term Actions (Next 2 Weeks)</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#change-request">4. Change Request</a><ul>
<li><a href="#pending-change-requests">Pending Change Requests</a></li>
<li><a href="#approved-changes">Approved Changes</a></li>
</ul>
</li>
<li><a href="#operation-update">5. Operation Update</a><ul>
<li><a href="#operational-issues-period-june-1-11-2025">Operational Issues (Period: June 1-11, 2025)</a></li>
<li><a href="#klop-performance-analysis-january-june-12-2025">KLOP Performance Analysis (January - June 12, 2025)</a><ul>
<li><a href="#user-registration-trends">User Registration Trends</a></li>
<li><a href="#monthly-registration-data">Monthly Registration Data:</a></li>
<li><a href="#performance-analysis">Performance Analysis:</a></li>
</ul>
</li>
<li><a href="#current-operational-status">Current Operational Status</a><ul>
<li><a href="#system-performance">System Performance</a></li>
<li><a href="#support-maintenance">Support &amp; Maintenance</a></li>
<li><a href="#immediate-operational-concerns">Immediate Operational Concerns</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#next-steps-recommendations">Next Steps &amp; Recommendations</a><ul>
<li><a href="#immediate-focus-areas-next-7-days">Immediate Focus Areas (Next 7 Days)</a></li>
<li><a href="#strategic-recommendations">Strategic Recommendations</a></li>
</ul>
</li>
<li><a href="#appendix">Appendix</a><ul>
<li><a href="#legend">Legend</a></li>
<li><a href="#contact-information">Contact Information</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div><h1 id="pmc-smartpay-project-update">PMC SmartPay Project Update</h1>
<p><strong>Date:</strong> June 26, 2025<br />
<strong>Project:</strong> Telkomsel SmartPay<br />
<strong>Document Type:</strong> Program Management Committee Update</p>
<hr />
<h2 id="executive-summary">Executive Summary</h2>
<p>This document presents the Program Management Committee update for the Telkomsel SmartPay project as of June 26, 2025. The project is currently in Sprint 2 phase with multiple workstreams progressing according to schedule.</p>
<hr />
<h2 id="table-of-contents">Table of Contents</h2>
<ol>
<li><a href="#program-schedule">Program Schedule, Milestones &amp; Key Management Highlights</a></li>
<li><a href="#workstream-update">Workstream Update</a></li>
<li><a href="#raid">Risks, Actions, Issues, Dependencies (RAID)</a></li>
<li><a href="#change-request">Change Request</a></li>
<li><a href="#operation-update">Operation Update</a></li>
</ol>
<hr />
<h2 id="program-schedule">1. Program Schedule, Milestones &amp; Key Management Highlights</h2>
<h3 id="high-level-project-timeline-2025">High-Level Project Timeline (2025)</h3>
<h4 id="current-status-were-here-week-of-june-26">Current Status: <strong>We're here</strong> (Week of June 26)</h4>
<h3 id="sprint-2-timeline">Sprint 2 Timeline</h3>
<ul>
<li><strong>Requirement Grooming</strong>: April 21 - May 2</li>
<li><strong>Sprint Planning</strong>: May 5 - May 16</li>
<li><strong>Development and UT</strong>: May 19 - May 30</li>
<li><strong>Testing Phase</strong>: June 5 - July 15</li>
</ul>
<h4 id="key-milestones-completed">Key Milestones Completed:</h4>
<ul>
<li><span class="status-badge status-completed">✅  </span><strong>BR Doc</strong>: May 2</li>
<li><span class="status-badge status-completed">✅  </span><strong>LLD &amp; IFA</strong>: May 23</li>
<li><span class="status-badge status-completed">✅  </span><strong>Design Solution</strong>: May 16</li>
<li><span class="status-badge status-completed">✅  </span><strong>G2-Sprint 2</strong>: June 5</li>
</ul>
<h4 id="upcoming-milestones">Upcoming Milestones:</h4>
<ul>
<li><span class="status-badge status-progress">🔄  </span><strong>G3-Sprint 2</strong>: July 21</li>
<li><span class="status-badge status-scheduled">📅  </span><strong>RFS</strong>: July 24</li>
</ul>
<h3 id="sprint-3-timeline">Sprint 3 Timeline</h3>
<ul>
<li><strong>Start Date</strong>: June 26 - July 9</li>
<li><strong>BR Doc</strong>: June 27</li>
<li><strong>Design Solution</strong>: July 11</li>
<li><strong>G2-Sprint 3</strong>: August 1</li>
<li><strong>G3-Sprint 3</strong>: September 17</li>
</ul>
<h3 id="testing-schedule">Testing Schedule</h3>
<h4 id="current-testing-phase">Current Testing Phase:</h4>
<ul>
<li><strong>SIT</strong>: June 5 - June 11 ✅</li>
<li><strong>UAT</strong>: June 12 - June 25 ✅</li>
<li><strong>Regression</strong>: June 26 - July 2 🔄</li>
<li><strong>NFT-PT</strong>: June 26 - July 9 🔄</li>
<li><strong>NFT-Security</strong>: June 26 - July 9 🔄</li>
<li><strong>NFT-Security Remediation</strong>: July 2 - July 15 📅</li>
</ul>
<h4 id="future-testing">Future Testing:</h4>
<ul>
<li><strong>RFS KLOP</strong>: July 25 - August 7</li>
<li><strong>FUT</strong>: July 25 - August 7</li>
<li><strong>RFC</strong>: August 11</li>
</ul>
<hr />
<h2 id="workstream-update">2. Workstream Update</h2>
<h3 id="smartpay-sprint-2-status">SmartPay Sprint 2 Status</h3>
<h4 id="progress-overview">Progress Overview:</h4>
<ul>
<li><strong>Overall Status</strong>: 🟢 In Progress (On-Track)</li>
<li><strong>UAT Progress</strong>: Currently in execution phase</li>
</ul>
<h3 id="sprint-2-backlog-items">Sprint 2 Backlog Items</h3>
<table>
<thead>
<tr>
<th>No</th>
<th>Feature</th>
<th>Priority</th>
<th>Impacted App</th>
<th>Surrounding</th>
<th>BR Status</th>
<th>Design Status</th>
</tr>
</thead>
<tbody>
<tr>
<td>1</td>
<td>Campaign - Automated notification for user registration drop off</td>
<td>High</td>
<td>Klop</td>
<td>-</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
</tr>
<tr>
<td>2</td>
<td>Outstanding debt handling for recycled MSISDN</td>
<td>High</td>
<td>Klop</td>
<td>UPS</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
</tr>
<tr>
<td>3</td>
<td>Save session last page</td>
<td>High</td>
<td>Klop</td>
<td>-</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
</tr>
<tr>
<td>4</td>
<td>Share Personal Data-Image File via API</td>
<td>High</td>
<td>Klop</td>
<td>-</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
</tr>
<tr>
<td>5</td>
<td>Service Fee Tiering</td>
<td>High</td>
<td>Amartha, Klop</td>
<td>-</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
</tr>
<tr>
<td>6</td>
<td>Automated reminder for billing, due, past due (via email, SMS, push notif MyTsel)</td>
<td>High</td>
<td>Amartha, Klop</td>
<td>NOIS</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
</tr>
<tr>
<td>7</td>
<td>Customer Lifecycle Enhancement</td>
<td>High</td>
<td>Klop</td>
<td>-</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
</tr>
<tr>
<td>8</td>
<td>SmartPay Dashboard</td>
<td>High</td>
<td>-</td>
<td>-</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
<tr>
<td>9</td>
<td>Telkomsel SmartPay di UPP (BR Existing)</td>
<td>High</td>
<td>UPP, Klop</td>
<td>MyTsel</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
</tr>
<tr>
<td>10</td>
<td>API Refund Handling</td>
<td>High</td>
<td>Klop</td>
<td>Amartha</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
<tr>
<td>11</td>
<td>Callback Repayment</td>
<td>High</td>
<td>Klop</td>
<td>Amartha</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
<tr>
<td>12</td>
<td>Liveness (BR Existing)</td>
<td>High</td>
<td>Klop/Amartha</td>
<td>-</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
<tr>
<td>13</td>
<td>Tracker/counter for page keuangan in MyTsel</td>
<td>High</td>
<td>MyTsel</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
<tr>
<td>14</td>
<td>Parameter campaign banner (tracker campaign)</td>
<td>High</td>
<td>MyTsel</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
<tr>
<td>15</td>
<td>Automated reminder for Billing, due-date, past-due (via WA)</td>
<td>High</td>
<td>KLOP</td>
<td>NOIS</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
<tr>
<td>16</td>
<td>[IT Internal] User token negative case handling</td>
<td>High</td>
<td>KLOP</td>
<td>-</td>
<td>N/A</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
<tr>
<td>17</td>
<td>Subscriber blocking</td>
<td>High</td>
<td>-</td>
<td>-</td>
<td>-</td>
<td>-</td>
</tr>
<tr>
<td>18</td>
<td>Installment Use Case</td>
<td>High</td>
<td>Amartha</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
<tr>
<td>19</td>
<td>Cash Loan Use Case</td>
<td>High</td>
<td>Amartha</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
</tbody>
</table>
<h3 id="key-features-delivered">Key Features Delivered</h3>
<h4 id="1-campaign-automated-notification-system">1. <strong>Campaign - Automated Notification System</strong></h4>
<ul>
<li><strong>Scope</strong>: User registration drop-off notifications</li>
<li><strong>Impact</strong>: Improved user conversion rates</li>
<li><strong>Status</strong>: Ready for testing</li>
</ul>
<h4 id="2-outstanding-debt-handling">2. <strong>Outstanding Debt Handling</strong></h4>
<ul>
<li><strong>Scope</strong>: Recycled MSISDN debt management</li>
<li><strong>Integration</strong>: UPS system integration</li>
<li><strong>Status</strong>: Implementation complete</li>
</ul>
<h4 id="3-service-fee-tiering">3. <strong>Service Fee Tiering</strong></h4>
<ul>
<li><strong>Scope</strong>: Dynamic fee structure implementation</li>
<li><strong>Applications</strong>: Amartha &amp; Klop integration</li>
<li><strong>Status</strong>: Ready for deployment</li>
</ul>
<h4 id="4-automated-reminder-system">4. <strong>Automated Reminder System</strong></h4>
<ul>
<li><strong>Channels</strong>: Email, SMS, Push notifications (MyTsel)</li>
<li><strong>Integration</strong>: NOIS system</li>
<li><strong>Scope</strong>: Billing, due dates, past due notifications</li>
</ul>
<h4 id="5-customer-lifecycle-enhancement">5. <strong>Customer Lifecycle Enhancement</strong></h4>
<ul>
<li><strong>Scope</strong>: End-to-end customer experience improvements</li>
<li><strong>Status</strong>: Development completed</li>
</ul>
<h3 id="amartha-product-backlog">Amartha Product Backlog</h3>
<table>
<thead>
<tr>
<th>No</th>
<th>Feature</th>
<th>Priority</th>
<th>Stakeholder</th>
<th>BRD/PRD</th>
<th>Development</th>
<th>IFA</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td>1</td>
<td>Service Fee Tiering</td>
<td>High</td>
<td>Klop</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td>Done - Waiting for Tsel Development</td>
</tr>
<tr>
<td>2</td>
<td>Dukcapil Validation</td>
<td>High</td>
<td>Klop</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td>ETA Release June 11, 2025</td>
</tr>
<tr>
<td>4</td>
<td>API Refund Handling</td>
<td>High</td>
<td>Klop/Elisa</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td>Done - Waiting for Tsel Development</td>
</tr>
<tr>
<td>5</td>
<td>Callback Repayment</td>
<td>High</td>
<td>Klop/Elisa</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td>Done - Waiting for Tsel Development</td>
</tr>
<tr>
<td>6</td>
<td>Liveness</td>
<td>Medium</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td>Waiting for API Onboarding</td>
</tr>
<tr>
<td>7</td>
<td>[Improvement] Refund Automation</td>
<td>Low</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td>Waiting for API Refund Handling</td>
</tr>
<tr>
<td>8</td>
<td>OCR Improvement</td>
<td>Medium</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td>Need to discuss with Telkomsel</td>
</tr>
<tr>
<td>9</td>
<td>Face Match</td>
<td>High</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td>Waiting for API Onboarding</td>
</tr>
<tr>
<td>10</td>
<td>Image Anti Tampering</td>
<td>High</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td>Waiting for API Onboarding</td>
</tr>
<tr>
<td>11</td>
<td>API Onboarding</td>
<td>High</td>
<td>Klop</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td>Done - Waiting for Tsel Development</td>
</tr>
<tr>
<td>12</td>
<td>Underwriting</td>
<td>High</td>
<td>Klop, Data</td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td>Waiting for API Onboarding</td>
</tr>
</tbody>
</table>
<h3 id="amartha-sprint-backlog">Amartha Sprint Backlog</h3>
<table>
<thead>
<tr>
<th>No</th>
<th>Feature</th>
<th>Priority</th>
<th>Stakeholder</th>
<th>Development Status</th>
<th>IFA</th>
<th>Note</th>
</tr>
</thead>
<tbody>
<tr>
<td>1</td>
<td>Event tracker repayment</td>
<td>High</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td>No Need</td>
<td>-</td>
</tr>
<tr>
<td>2</td>
<td>Dukcapil validation</td>
<td>High</td>
<td>Klop</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td>No Need</td>
<td>-</td>
</tr>
<tr>
<td>3</td>
<td>Payment option reorder</td>
<td>Medium</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td>No Need</td>
<td>-</td>
</tr>
<tr>
<td>4</td>
<td>Tech debt billing</td>
<td>High</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td>No Need</td>
<td>-</td>
</tr>
<tr>
<td>5</td>
<td>Improvement Dana &amp; Gopay</td>
<td>High</td>
<td>-</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
<td>No Need</td>
<td>-</td>
</tr>
</tbody>
</table>
<hr />
<h2 id="raid">3. Risks, Actions, Issues, Dependencies (RAID)</h2>
<h3 id="key-raid-items">Key RAID Items</h3>
<table>
<thead>
<tr>
<th>No.</th>
<th>Category</th>
<th>Type</th>
<th>Backlog</th>
<th>Details</th>
<th>Current PIC</th>
<th>Due Date</th>
<th>Status</th>
</tr>
</thead>
<tbody>
<tr>
<td>1</td>
<td>Action</td>
<td>BAU</td>
<td>Sync Data Reporting</td>
<td>Amartha provide daily data in Minio, KLOP used and enrich the data to DFS Ops</td>
<td>DFS OPS (Fikhri)</td>
<td>June 26</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
<tr>
<td>4</td>
<td>Action</td>
<td>Development</td>
<td>Refresh Token</td>
<td>IT EA team raised concerns regarding the Refresh Token Solution</td>
<td>PMO (Erwin)</td>
<td>TBD</td>
<td><span class="status-badge status-progress">⏸️ On Hold</span></td>
</tr>
<tr>
<td>14</td>
<td>Action</td>
<td>BAU</td>
<td>Manual Image File Sharing</td>
<td>Mechanism of sharing manual image file confirmation from Amartha</td>
<td>L1 Bram, PMO (Hary)</td>
<td>TBD</td>
<td><span class="status-badge status-progress">🔄 In Progress</span></td>
</tr>
</tbody>
</table>
<h3 id="detailed-raid-analysis">Detailed RAID Analysis</h3>
<h4 id="1-sync-data-reporting-in-progress">1. <strong>Sync Data Reporting</strong> (In Progress)</h4>
<ul>
<li><strong>Issue</strong>: Amartha needs to provide daily data in Minio for KLOP to enrich and send to DFS Ops</li>
<li><strong>Timeline Updates</strong>:</li>
<li>[15 Apr] Data available up to April 14</li>
<li>[23 Apr] DFS Ops confirmed data received but timing not as expected</li>
<li>[2 May] Extended development needed for different data sources</li>
<li>[15 May] Amartha informed ready by June 6</li>
<li>[27 May] Semi-automated delivery on June 5</li>
<li>[11 Jun] Configuration update on min.io</li>
<li>[18 Jun] Data sent, DFS ops requested June 4-12 files separately</li>
<li>[19 Jun] DFS ops confirmed OK, monitoring until June 26</li>
<li><strong>Current Status</strong>: Monitoring phase until June 26</li>
</ul>
<h4 id="2-refresh-token-solution-on-hold">2. <strong>Refresh Token Solution</strong> (On Hold)</h4>
<ul>
<li><strong>Issue</strong>: IT EA team raised concerns about current refresh token solution</li>
<li><strong>Impact</strong>: Solution will not address possible token problems on Amartha side</li>
<li><strong>Decision</strong>: Aligned with ITSA - current refresh solution not needed</li>
<li><strong>Status</strong>: On hold pending further review</li>
</ul>
<h4 id="3-manual-image-file-sharing-in-progress">3. <strong>Manual Image File Sharing</strong> (In Progress)</h4>
<ul>
<li><strong>Issue</strong>: Need mechanism for sharing manual image file confirmation from Amartha</li>
<li><strong>Progress</strong>:</li>
<li>[22 May] Data requested by event, SOP request informed May 23</li>
<li>[23 May] Amartha confirmation email sent</li>
<li>[26 May] Daily delivery expectation, tower team assessment</li>
<li>[28 May] Delivered to L1 team</li>
<li>[3 Jun] L1 requested Security Clearance from IT Sec</li>
<li>[11 Jun] ICT compliance requirements identified</li>
<li><strong>Current Status</strong>: Awaiting compliance requirements fulfillment</li>
</ul>
<h3 id="current-risk-assessment">Current Risk Assessment</h3>
<h4 id="high-priority-risks"><span class="status-badge status-risk">🔴 High Priority Risks</span></h4>
<ol>
<li><strong>Data Synchronization Dependencies</strong></li>
<li>Amartha data delivery timing issues</li>
<li>
<p>DFS Ops integration dependencies</p>
</li>
<li>
<p><strong>Security Compliance Requirements</strong></p>
</li>
<li>ICT compliance requirements for manual image file sharing</li>
<li>
<p>Security clearance processes causing delays</p>
</li>
<li>
<p><strong>Testing Timeline Pressure</strong></p>
</li>
<li>Concurrent testing phases (NFT-PT, NFT-Security)</li>
<li>Resource allocation challenges</li>
</ol>
<h4 id="medium-priority-issues">🟡 Medium Priority Issues</h4>
<ol>
<li><strong>SmartPay Dashboard Design</strong></li>
<li>Design phase still in progress</li>
<li>
<p>Potential impact on Sprint 2 completion</p>
</li>
<li>
<p><strong>Token Management Architecture</strong></p>
</li>
<li>Refresh token solution on hold</li>
<li>Need alternative approach for token handling</li>
</ol>
<h3 id="action-items">Action Items</h3>
<h4 id="immediate-actions-this-week">Immediate Actions (This Week)</h4>
<ul>
<li>[ ] Monitor Amartha data sync until June 26</li>
<li>[ ] Complete UAT execution for all Sprint 2 features</li>
<li>[ ] Initiate Regression testing phase</li>
<li>[ ] Begin NFT-PT and NFT-Security testing</li>
<li>[ ] Finalize SmartPay Dashboard design</li>
</ul>
<h4 id="short-term-actions-next-2-weeks">Short-term Actions (Next 2 Weeks)</h4>
<ul>
<li>[ ] Resolve ICT compliance requirements for image file sharing</li>
<li>[ ] Complete all Sprint 2 testing phases</li>
<li>[ ] Prepare for G3-Sprint 2 milestone</li>
<li>[ ] Review refresh token solution alternatives</li>
<li>[ ] Initiate Sprint 3 requirement grooming</li>
</ul>
<hr />
<h2 id="change-request">4. Change Request</h2>
<h3 id="pending-change-requests">Pending Change Requests</h3>
<p><em>[This section would contain any formal change requests - content not fully visible in the extracted text]</em></p>
<h3 id="approved-changes">Approved Changes</h3>
<p><em>[Details of approved changes would be listed here]</em></p>
<hr />
<h2 id="operation-update">5. Operation Update</h2>
<h3 id="operational-issues-period-june-1-11-2025">Operational Issues (Period: June 1-11, 2025)</h3>
<table>
<thead>
<tr>
<th>No</th>
<th>Issue Type</th>
<th>Root Cause</th>
<th>Action</th>
<th>Raise Date</th>
<th>Plan</th>
<th>Actual</th>
<th>Severity</th>
<th>Status</th>
<th>PIC</th>
</tr>
</thead>
<tbody>
<tr>
<td>1</td>
<td>Anomaly Data Issue</td>
<td>Data captured through OCR and submitted directly by user</td>
<td>• Track end-to-end process<br>• List investigation results<br>• Suggest solution</td>
<td>June 4</td>
<td>June 4-11</td>
<td>June 4-11</td>
<td>High</td>
<td><span class="status-badge status-completed">✅ Completed</span></td>
<td>Avian/Dodi</td>
</tr>
<tr>
<td>2</td>
<td>Pending Support Refresh Token</td>
<td>Amartha internal incident caused postponement</td>
<td>• Waiting for next schedule confirmation</td>
<td>June 10</td>
<td>June 11</td>
<td>TBC</td>
<td>High</td>
<td><span class="status-badge status-progress">⏸️ Not Started</span></td>
<td>Avian/Dodi</td>
</tr>
</tbody>
</table>
<h3 id="klop-performance-analysis-january-june-12-2025">KLOP Performance Analysis (January - June 12, 2025)</h3>
<h4 id="user-registration-trends">User Registration Trends</h4>
<p><strong>Key Highlights:</strong>
- <strong>Peak Performance</strong>: April 2025 with highest user registration traffic
- <strong>Significant Drop</strong>: 71.88% decrease in May compared to April
- <strong>Current Status</strong>: June showing continued low registration numbers</p>
<h4 id="monthly-registration-data">Monthly Registration Data:</h4>
<table>
<thead>
<tr>
<th>Month</th>
<th>Registered Users</th>
<th>Rejected Users</th>
<th>Total Applications</th>
</tr>
</thead>
<tbody>
<tr>
<td>January</td>
<td>194</td>
<td>13</td>
<td>207</td>
</tr>
<tr>
<td>February</td>
<td>823</td>
<td>60</td>
<td>883</td>
</tr>
<tr>
<td>March</td>
<td>24,967</td>
<td>1,904</td>
<td>26,877</td>
</tr>
<tr>
<td>April</td>
<td>30,027</td>
<td>2,921</td>
<td>32,955</td>
</tr>
<tr>
<td>May</td>
<td>8,317</td>
<td>898</td>
<td>9,263</td>
</tr>
<tr>
<td>June (1-12)</td>
<td>1,168</td>
<td>190</td>
<td>1,376</td>
</tr>
</tbody>
</table>
<h4 id="performance-analysis">Performance Analysis:</h4>
<ul>
<li><strong>REGISTERED</strong>: Users approved by Amartha who became Telkomsel SmartPay customers</li>
<li><strong>REJECTED</strong>: Users rejected by Amartha during the approval process</li>
<li><strong>Approval Rate</strong>: Consistently high across all months (&gt;89%)</li>
<li><strong>Concern</strong>: Dramatic drop in May requires investigation and corrective action</li>
</ul>
<h3 id="current-operational-status">Current Operational Status</h3>
<h4 id="system-performance">System Performance</h4>
<ul>
<li><strong>Availability</strong>: Monitoring in progress</li>
<li><strong>Performance Metrics</strong>: Within acceptable thresholds for approved users</li>
<li><strong>User Adoption</strong>: Significant decline requiring immediate attention</li>
</ul>
<h4 id="support-maintenance">Support &amp; Maintenance</h4>
<ul>
<li><strong>Issue Resolution</strong>:</li>
<li>1 critical issue resolved (Anomaly Data Issue)</li>
<li>1 pending issue (Refresh Token Support)</li>
<li><strong>User Support</strong>: Help desk operational</li>
<li><strong>Documentation</strong>: Updated for new features</li>
</ul>
<h4 id="immediate-operational-concerns">Immediate Operational Concerns</h4>
<ol>
<li><strong>User Registration Drop</strong>: 71.88% decrease in May needs root cause analysis</li>
<li><strong>Refresh Token Support</strong>: Pending resolution due to Amartha internal issues</li>
<li><strong>Data Quality</strong>: OCR-related anomalies requiring process improvements</li>
</ol>
<hr />
<h2 id="next-steps-recommendations">Next Steps &amp; Recommendations</h2>
<h3 id="immediate-focus-areas-next-7-days">Immediate Focus Areas (Next 7 Days)</h3>
<ol>
<li><strong>Complete UAT Phase</strong>: Ensure all test cases are executed successfully</li>
<li><strong>Regression Testing</strong>: Begin comprehensive regression testing</li>
<li><strong>Security Testing</strong>: Initiate NFT-Security testing phase</li>
<li><strong>Design Completion</strong>: Finalize pending design work for Dashboard and UPP</li>
</ol>
<h3 id="strategic-recommendations">Strategic Recommendations</h3>
<ol>
<li><strong>Resource Allocation</strong>: Consider additional testing resources for concurrent NFT phases</li>
<li><strong>Risk Mitigation</strong>: Develop contingency plans for integration dependencies</li>
<li><strong>Communication</strong>: Enhance stakeholder communication for Sprint 3 preparation</li>
<li><strong>Quality Assurance</strong>: Implement additional quality gates before G3-Sprint 2</li>
</ol>
<hr />
<h2 id="appendix">Appendix</h2>
<h3 id="legend">Legend</h3>
<ul>
<li><span class="status-badge status-completed">✅  </span><strong>Completed</strong>: Task/milestone successfully finished</li>
<li><span class="status-badge status-progress">🔄  </span><strong>In Progress</strong>: Currently being worked on</li>
<li><span class="status-badge status-scheduled">📅  </span><strong>Scheduled</strong>: Planned for future execution</li>
<li><span class="status-badge status-risk">🔴  </span><strong>High Risk</strong>: Requires immediate attention</li>
<li>🟡 <strong>Medium Risk</strong>: Monitor closely</li>
<li>🟢 <strong>On Track</strong>: Progressing as planned</li>
</ul>
<h3 id="contact-information">Contact Information</h3>
<ul>
<li><strong>Project Manager</strong>: [To be filled]</li>
<li><strong>Technical Lead</strong>: [To be filled]</li>
<li><strong>Business Owner</strong>: [To be filled]</li>
</ul>
<hr />
<p><em>Document prepared by: PMC Team</em><br />
<em>Last updated: June 26, 2025</em><br />
<em>Next update: July 3, 2025</em></p>
        </div>
        <div class="footer">
            <div class="print-only">Document generated from Markdown</div>
            <div>Converted from Markdown to HTML • PMC_SmartPay_Update_20250626.md</div>
        </div>
    </div>
</body>
</html>