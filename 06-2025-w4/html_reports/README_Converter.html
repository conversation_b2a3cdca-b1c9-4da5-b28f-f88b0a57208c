<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown to HTML Converter</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .meta {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .toc {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .toc h2 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        .toc a {
            color: #555;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 3px;
            transition: all 0.3s;
        }
        
        .toc a:hover {
            background: #667eea;
            color: white;
        }
        
        h1, h2, h3, h4, h5, h6 {
            margin: 30px 0 15px 0;
            color: #2c3e50;
        }
        
        h1 { font-size: 2.2em; border-bottom: 3px solid #667eea; padding-bottom: 10px; }
        h2 { font-size: 1.8em; border-bottom: 2px solid #ddd; padding-bottom: 8px; }
        h3 { font-size: 1.5em; color: #667eea; }
        h4 { font-size: 1.3em; }
        
        p {
            margin: 15px 0;
            text-align: justify;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background: #f5f5f5;
        }
        
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            color: #e74c3c;
        }
        
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 20px 0;
        }
        
        pre code {
            background: none;
            color: inherit;
            padding: 0;
        }
        
        blockquote {
            border-left: 4px solid #667eea;
            margin: 20px 0;
            padding: 15px 20px;
            background: #f8f9fa;
            font-style: italic;
        }
        
        ul, ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 8px 0;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin: 2px;
        }
        
        .status-completed { background: #d4edda; color: #155724; }
        .status-progress { background: #fff3cd; color: #856404; }
        .status-scheduled { background: #cce5ff; color: #004085; }
        .status-risk { background: #f8d7da; color: #721c24; }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            body { padding: 10px; }
            .content { padding: 20px; }
            .header { padding: 20px; }
            .header h1 { font-size: 2em; }
            table { font-size: 0.9em; }
        }
        
        .print-only { display: none; }
        
        @media print {
            body { background: white; padding: 0; }
            .container { box-shadow: none; }
            .header { background: #667eea !important; }
            .print-only { display: block; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Markdown to HTML Converter</h1>
            <div class="meta">Generated on June 26, 2025 at 10:36 AM</div>
        </div>
        <div class="content">
            <div class="toc"><h2>📋 Table of Contents</h2><div class="toc">
<ul>
<li><a href="#markdown-to-html-converter">Markdown to HTML Converter</a><ul>
<li><a href="#features">Features</a></li>
<li><a href="#installation">Installation</a><ul>
<li><a href="#prerequisites">Prerequisites</a></li>
<li><a href="#install-dependencies">Install Dependencies</a></li>
</ul>
</li>
<li><a href="#usage">Usage</a><ul>
<li><a href="#basic-usage">Basic Usage</a></li>
<li><a href="#using-the-shell-script-linuxmacos">Using the Shell Script (Linux/macOS)</a></li>
<li><a href="#command-line-options">Command Line Options</a><ul>
<li><a href="#python-script-options">Python Script Options:</a></li>
<li><a href="#shell-script-options">Shell Script Options:</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#examples">Examples</a><ul>
<li><a href="#convert-your-project-reports">Convert Your Project Reports</a></li>
<li><a href="#using-with-your-current-files">Using with Your Current Files</a></li>
</ul>
</li>
<li><a href="#output-features">Output Features</a><ul>
<li><a href="#visual-elements">🎨 Visual Elements</a></li>
<li><a href="#content-organization">📋 Content Organization</a></li>
<li><a href="#table-enhancement">📊 Table Enhancement</a></li>
<li><a href="#code-display">💻 Code Display</a></li>
</ul>
</li>
<li><a href="#supported-markdown-extensions">Supported Markdown Extensions</a></li>
<li><a href="#customization">Customization</a><ul>
<li><a href="#key-css-classes">Key CSS Classes:</a></li>
</ul>
</li>
<li><a href="#browser-compatibility">Browser Compatibility</a></li>
<li><a href="#print-support">Print Support</a></li>
<li><a href="#troubleshooting">Troubleshooting</a><ul>
<li><a href="#common-issues">Common Issues:</a></li>
</ul>
</li>
<li><a href="#license">License</a></li>
<li><a href="#contributing">Contributing</a></li>
</ul>
</li>
</ul>
</div>
</div><h1 id="markdown-to-html-converter">Markdown to HTML Converter</h1>
<p>A powerful Python script that converts Markdown files to beautifully styled HTML documents with professional formatting, responsive design, and enhanced features.</p>
<h2 id="features">Features</h2>
<p>✨ <strong>Professional Styling</strong>
- Modern, responsive design that works on all devices
- Beautiful gradient headers and clean typography
- Professional color scheme and spacing</p>
<p>📋 <strong>Automatic Table of Contents</strong>
- Generates clickable TOC from headers
- Smooth navigation within documents
- Collapsible sections for better organization</p>
<p>📊 <strong>Enhanced Tables</strong>
- Styled tables with hover effects
- Responsive table design
- Professional borders and spacing</p>
<p>🎨 <strong>Status Badge Support</strong>
- Automatically converts emoji status indicators to styled badges
- Supports: <span class="status-badge status-completed">✅ Completed, <span class="status-badge status-progress">🔄 In Progress, <span class="status-badge status-scheduled">📅 Scheduled, <span class="status-badge status-risk">🔴 High Risk, <span class="status-badge status-progress">⏸️ On Hold</span></span></span></span></span></p>
<p>💻 <strong>Code Highlighting</strong>
- Syntax highlighting for code blocks
- Inline code styling
- Dark theme for code blocks</p>
<p>📱 <strong>Responsive Design</strong>
- Mobile-friendly layout
- Print-optimized styling
- Cross-browser compatibility</p>
<h2 id="installation">Installation</h2>
<h3 id="prerequisites">Prerequisites</h3>
<ul>
<li>Python 3.6 or higher</li>
<li>pip (Python package installer)</li>
</ul>
<h3 id="install-dependencies">Install Dependencies</h3>
<div class="codehilite"><pre><span></span><code>pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements.txt
</code></pre></div>

<p>Or install manually:</p>
<div class="codehilite"><pre><span></span><code>pip<span class="w"> </span>install<span class="w"> </span>markdown
</code></pre></div>

<h2 id="usage">Usage</h2>
<h3 id="basic-usage">Basic Usage</h3>
<p><strong>Convert single file:</strong></p>
<div class="codehilite"><pre><span></span><code>python<span class="w"> </span>markdown_to_html.py<span class="w"> </span>report.md
</code></pre></div>

<p><strong>Convert with custom output:</strong></p>
<div class="codehilite"><pre><span></span><code>python<span class="w"> </span>markdown_to_html.py<span class="w"> </span>report.md<span class="w"> </span>output.html
</code></pre></div>

<p><strong>Batch convert multiple files:</strong></p>
<div class="codehilite"><pre><span></span><code>python<span class="w"> </span>markdown_to_html.py<span class="w"> </span>--batch<span class="w"> </span>*.md
</code></pre></div>

<p><strong>Batch convert to specific directory:</strong></p>
<div class="codehilite"><pre><span></span><code>python<span class="w"> </span>markdown_to_html.py<span class="w"> </span>--batch<span class="w"> </span>--output-dir<span class="w"> </span>html_files<span class="w"> </span>*.md
</code></pre></div>

<h3 id="using-the-shell-script-linuxmacos">Using the Shell Script (Linux/macOS)</h3>
<p>Make the script executable:</p>
<div class="codehilite"><pre><span></span><code>chmod<span class="w"> </span>+x<span class="w"> </span>convert_md.sh
</code></pre></div>

<p><strong>Basic conversion:</strong></p>
<div class="codehilite"><pre><span></span><code>./convert_md.sh<span class="w"> </span>report.md
</code></pre></div>

<p><strong>Batch convert with browser opening:</strong></p>
<div class="codehilite"><pre><span></span><code>./convert_md.sh<span class="w"> </span>-b<span class="w"> </span>-w<span class="w"> </span>*.md
</code></pre></div>

<p><strong>Convert to specific directory:</strong></p>
<div class="codehilite"><pre><span></span><code>./convert_md.sh<span class="w"> </span>-o<span class="w"> </span>html_output<span class="w"> </span>*.md
</code></pre></div>

<h3 id="command-line-options">Command Line Options</h3>
<h4 id="python-script-options">Python Script Options:</h4>
<ul>
<li><code>input</code>: Input markdown file(s)</li>
<li><code>-o, --output</code>: Output HTML file (for single file conversion)</li>
<li><code>--batch</code>: Enable batch conversion mode</li>
<li><code>--output-dir</code>: Output directory for batch conversion</li>
</ul>
<h4 id="shell-script-options">Shell Script Options:</h4>
<ul>
<li><code>-o, --output-dir DIR</code>: Output directory for HTML files</li>
<li><code>-b, --batch</code>: Enable batch mode</li>
<li><code>-w, --open</code>: Open generated HTML in browser</li>
<li><code>-h, --help</code>: Show help message</li>
</ul>
<h2 id="examples">Examples</h2>
<h3 id="convert-your-project-reports">Convert Your Project Reports</h3>
<div class="codehilite"><pre><span></span><code><span class="c1"># Convert all your weekly reports</span>
python<span class="w"> </span>markdown_to_html.py<span class="w"> </span>--batch<span class="w"> </span>--output-dir<span class="w"> </span>reports_html<span class="w"> </span>*.md

<span class="c1"># Convert specific files</span>
python<span class="w"> </span>markdown_to_html.py<span class="w"> </span>PMC_SmartPay_Update_20250626.md<span class="w"> </span>Yuda_Weekly_Report_W3_Jun_20.md
</code></pre></div>

<h3 id="using-with-your-current-files">Using with Your Current Files</h3>
<div class="codehilite"><pre><span></span><code><span class="c1"># Convert the summary report</span>
python<span class="w"> </span>markdown_to_html.py<span class="w"> </span>Weekly_Summary_Report_June_2025.md

<span class="c1"># Convert all markdown files in current directory</span>
./convert_md.sh<span class="w"> </span>-b<span class="w"> </span>-w<span class="w"> </span>*.md
</code></pre></div>

<h2 id="output-features">Output Features</h2>
<p>The generated HTML includes:</p>
<h3 id="visual-elements">🎨 <strong>Visual Elements</strong></h3>
<ul>
<li>Professional header with gradient background</li>
<li>Clean, readable typography</li>
<li>Responsive layout for all screen sizes</li>
<li>Print-friendly styling</li>
</ul>
<h3 id="content-organization">📋 <strong>Content Organization</strong></h3>
<ul>
<li>Automatic table of contents generation</li>
<li>Clickable navigation links</li>
<li>Hierarchical header styling</li>
<li>Status badge conversion</li>
</ul>
<h3 id="table-enhancement">📊 <strong>Table Enhancement</strong></h3>
<ul>
<li>Styled tables with hover effects</li>
<li>Responsive table design</li>
<li>Professional appearance</li>
</ul>
<h3 id="code-display">💻 <strong>Code Display</strong></h3>
<ul>
<li>Syntax highlighting</li>
<li>Dark theme code blocks</li>
<li>Inline code styling</li>
</ul>
<h2 id="supported-markdown-extensions">Supported Markdown Extensions</h2>
<ul>
<li><strong>Tables</strong>: Full table support with styling</li>
<li><strong>Fenced Code Blocks</strong>: Syntax highlighting</li>
<li><strong>Table of Contents</strong>: Auto-generated from headers</li>
<li><strong>Attributes</strong>: Custom attributes for elements</li>
<li><strong>Definition Lists</strong>: Enhanced list formatting</li>
<li><strong>Abbreviations</strong>: Expandable abbreviations</li>
<li><strong>Footnotes</strong>: Reference-style footnotes</li>
</ul>
<h2 id="customization">Customization</h2>
<p>The HTML template includes comprehensive CSS that can be customized by modifying the <code>get_html_template()</code> function in <code>markdown_to_html.py</code>.</p>
<h3 id="key-css-classes">Key CSS Classes:</h3>
<ul>
<li><code>.container</code>: Main content wrapper</li>
<li><code>.header</code>: Document header section</li>
<li><code>.content</code>: Main content area</li>
<li><code>.toc</code>: Table of contents styling</li>
<li><code>.status-badge</code>: Status indicator badges</li>
<li><code>.footer</code>: Document footer</li>
</ul>
<h2 id="browser-compatibility">Browser Compatibility</h2>
<ul>
<li><span class="status-badge status-completed">✅ Chrome/Chromium (all versions)</span></li>
<li><span class="status-badge status-completed">✅ Firefox (all versions)</span></li>
<li><span class="status-badge status-completed">✅ Safari (all versions)</span></li>
<li><span class="status-badge status-completed">✅ Edge (all versions)</span></li>
<li><span class="status-badge status-completed">✅ Mobile browsers</span></li>
</ul>
<h2 id="print-support">Print Support</h2>
<p>The generated HTML includes print-optimized CSS for professional document printing:
- Optimized page breaks
- Print-specific styling
- Clean black and white output</p>
<h2 id="troubleshooting">Troubleshooting</h2>
<h3 id="common-issues">Common Issues:</h3>
<p><strong>"markdown module not found"</strong></p>
<div class="codehilite"><pre><span></span><code>pip<span class="w"> </span>install<span class="w"> </span>markdown
</code></pre></div>

<p><strong>Permission denied (shell script)</strong></p>
<div class="codehilite"><pre><span></span><code>chmod<span class="w"> </span>+x<span class="w"> </span>convert_md.sh
</code></pre></div>

<p><strong>Python not found</strong>
- Ensure Python 3.6+ is installed
- Try using <code>python3</code> instead of <code>python</code></p>
<h2 id="license">License</h2>
<p>This script is provided as-is for project use. Feel free to modify and distribute as needed.</p>
<h2 id="contributing">Contributing</h2>
<p>To improve the converter:
1. Modify the CSS in <code>get_html_template()</code>
2. Add new markdown extensions in the <code>convert_markdown_to_html()</code> function
3. Enhance the status badge patterns in <code>enhance_content()</code></p>
<hr />
<p><em>Happy converting! 🚀</em></p>
        </div>
        <div class="footer">
            <div class="print-only">Document generated from Markdown</div>
            <div>Converted from Markdown to HTML • README_Converter.md</div>
        </div>
    </div>
</body>
</html>