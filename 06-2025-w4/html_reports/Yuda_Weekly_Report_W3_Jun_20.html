<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yuda - Weekly Report (W3 Jun - 20 Jun)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .meta {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .toc {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .toc h2 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        .toc a {
            color: #555;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 3px;
            transition: all 0.3s;
        }
        
        .toc a:hover {
            background: #667eea;
            color: white;
        }
        
        h1, h2, h3, h4, h5, h6 {
            margin: 30px 0 15px 0;
            color: #2c3e50;
        }
        
        h1 { font-size: 2.2em; border-bottom: 3px solid #667eea; padding-bottom: 10px; }
        h2 { font-size: 1.8em; border-bottom: 2px solid #ddd; padding-bottom: 8px; }
        h3 { font-size: 1.5em; color: #667eea; }
        h4 { font-size: 1.3em; }
        
        p {
            margin: 15px 0;
            text-align: justify;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background: #f5f5f5;
        }
        
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            color: #e74c3c;
        }
        
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 20px 0;
        }
        
        pre code {
            background: none;
            color: inherit;
            padding: 0;
        }
        
        blockquote {
            border-left: 4px solid #667eea;
            margin: 20px 0;
            padding: 15px 20px;
            background: #f8f9fa;
            font-style: italic;
        }
        
        ul, ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 8px 0;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin: 2px;
        }
        
        .status-completed { background: #d4edda; color: #155724; }
        .status-progress { background: #fff3cd; color: #856404; }
        .status-scheduled { background: #cce5ff; color: #004085; }
        .status-risk { background: #f8d7da; color: #721c24; }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            body { padding: 10px; }
            .content { padding: 20px; }
            .header { padding: 20px; }
            .header h1 { font-size: 2em; }
            table { font-size: 0.9em; }
        }
        
        .print-only { display: none; }
        
        @media print {
            body { background: white; padding: 0; }
            .container { box-shadow: none; }
            .header { background: #667eea !important; }
            .print-only { display: block; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Yuda - Weekly Report (W3 Jun - 20 Jun)</h1>
            <div class="meta">Generated on June 26, 2025 at 10:36 AM</div>
        </div>
        <div class="content">
            <div class="toc"><h2>📋 Table of Contents</h2><div class="toc">
<ul>
<li><a href="#yuda-weekly-report-w3-jun-20-jun">Yuda - Weekly Report (W3 Jun - 20 Jun)</a><ul>
<li><a href="#digihub-project-status">DIGIHUB Project Status</a><ul>
<li><a href="#sprint-10-overview">Sprint 10 Overview</a></li>
<li><a href="#testing-schedule">Testing Schedule</a></li>
<li><a href="#sprint-11-drop-1-settlement-automation-2fa">Sprint 11 (Drop 1): Settlement Automation &amp; 2FA</a><ul>
<li><a href="#team-allocation-workload">Team Allocation &amp; Workload</a></li>
<li><a href="#key-activities-schedule">Key Activities Schedule</a></li>
</ul>
</li>
<li><a href="#gate-2-documents-sprint-11-12">Gate 2 Documents - Sprint 11 &amp; 12</a></li>
</ul>
</li>
<li><a href="#operational-activities">Operational Activities</a><ul>
<li><a href="#key-operational-items-week-of-june-20th">Key Operational Items (Week of June 20th)</a></li>
<li><a href="#sprint-2-activities">Sprint 2 Activities</a><ul>
<li><a href="#key-milestones">Key Milestones</a></li>
<li><a href="#development-timeline">Development Timeline</a></li>
<li><a href="#key-features-in-development">Key Features in Development</a></li>
<li><a href="#testing-schedule_1">Testing Schedule</a></li>
<li><a href="#release-timeline">Release Timeline</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#action-items-priorities">Action Items &amp; Priorities</a><ul>
<li><a href="#immediate-actions">Immediate Actions</a></li>
<li><a href="#medium-term-actions-rfs-may-27">Medium-term Actions (RFS May 27)</a></li>
<li><a href="#long-term-actions-rfs-june-11">Long-term Actions (RFS June 11)</a></li>
</ul>
</li>
<li><a href="#in-progress-items-15-items">In-Progress Items (15 items)</a><ul>
<li><a href="#data-management">Data Management</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div><h1 id="yuda-weekly-report-w3-jun-20-jun">Yuda - Weekly Report (W3 Jun - 20 Jun)</h1>
<h2 id="digihub-project-status">DIGIHUB Project Status</h2>
<h3 id="sprint-10-overview">Sprint 10 Overview</h3>
<ul>
<li><strong>Status</strong>: Grooming, Assessment, Development</li>
<li><strong>API Active Status</strong>: In progress</li>
<li><strong>API Compound</strong>: In development</li>
</ul>
<h3 id="testing-schedule">Testing Schedule</h3>
<ul>
<li><strong>SIT</strong>: Complete all test executions by April 15, 2025</li>
<li><strong>UAT</strong>: Finished all planned test executions on day 2, ahead of original schedule (April 16-23, 2025)</li>
<li><strong>Regression</strong>: April 23 - complete all test executions by April 22, 2025</li>
<li><strong>NFT</strong>: Progress - April 24 - May 6</li>
<li><strong>Pentest</strong>: April 25 - May 7</li>
<li><strong>RFS</strong>: May 22</li>
</ul>
<h3 id="sprint-11-drop-1-settlement-automation-2fa">Sprint 11 (Drop 1): Settlement Automation &amp; 2FA</h3>
<h4 id="team-allocation-workload">Team Allocation &amp; Workload</h4>
<p>The average total estimated days per assignee across the sprint: <strong>13.92 days</strong></p>
<ul>
<li><strong>Backend (Hanapi)</strong>: 16.25 days (16.76% above average)</li>
<li><strong>Frontend (Fanny)</strong>: 13.75 days (1.20% below average)</li>
<li><strong>Backend (Fatur)</strong>: 11.75 days (15.57% below average)</li>
</ul>
<h4 id="key-activities-schedule">Key Activities Schedule</h4>
<p><strong>Sprint Planning</strong>:
- Sprint 11 &amp; 12: Scheduled for June 18th and July 7th, 2025 (one day each)</p>
<p><strong>Development Phase</strong>:
- Sprint 11: June 19th to July 4th, 2025
- Sprint 12: July 8th to July 16th, 2025</p>
<p><strong>Testing and Validation</strong>:
- UI/UX Checking: July 17th-18th, 2025
- SIT (System Integration Testing): July 21st-28th, 2025
- UAT &amp; Regression: July 29th-August 7th, 2025
- NFT (Non-Functional Testing): August 8th-19th, 2025
- VA (Vulnerability Assessment): August 8th-22nd, 2025
- Pentest (Penetration Testing): August 11th-25th, 2025
- Remediation &amp; Retest: August 26th-29th, 2025
- FUT (Final User Testing): September 3rd-17th, 2025</p>
<p><strong>Deployment/Release</strong>:
- RFS (Ready for Service): September 1st-2nd, 2025
- RFC (Ready for Change): September 18th-19th, 2025</p>
<h3 id="gate-2-documents-sprint-11-12">Gate 2 Documents - Sprint 11 &amp; 12</h3>
<p><strong>Document Preparation Schedule</strong>:
- <strong>SRS</strong> (System Requirements Specification): June 19th-24th, 2025
- <strong>SIT TCs</strong> (System Integration Test Cases): June 19th-July 17th, 2025 (± 70 TCs)
- <strong>UT Document Result &amp; Sign Off</strong>: July 18th-19th, 2025
- <strong>IP/Port Information</strong> for VA &amp; Security Test: August 1st, 2025
- <strong>API List for NFT</strong>: July 16th-17th, 2025</p>
<h2 id="operational-activities">Operational Activities</h2>
<h3 id="key-operational-items-week-of-june-20th">Key Operational Items (Week of June 20th)</h3>
<ul>
<li><strong>2FA Implementation on Digihub CMS</strong>: June 18th</li>
<li><strong>Assessment Policy on WAF</strong>: In progress</li>
<li><strong>Critical Issue Identified</strong>: Business team identified critical anomaly in transaction traffic between Splunk logs and metering database (Thursday, June 26, 2025 09:31)</li>
</ul>
<h3 id="sprint-2-activities">Sprint 2 Activities</h3>
<h4 id="key-milestones">Key Milestones</h4>
<ul>
<li><strong>BR Doc</strong>: Complete by May 2</li>
<li><strong>LLD &amp; IFA</strong>: Complete by May 23</li>
<li><strong>Design Solution</strong>: Complete by May 16</li>
<li><strong>G2-Sprint 2</strong>: By June 5</li>
<li><strong>G3-Sprint 2</strong>: By July 21</li>
</ul>
<h4 id="development-timeline">Development Timeline</h4>
<ul>
<li><strong>Requirement Grooming</strong>: April 21 – May 2</li>
<li><strong>Sprint Planning</strong>: May 5 – May 16</li>
<li><strong>Development and UT</strong>: May 19 – June 2</li>
</ul>
<h4 id="key-features-in-development">Key Features in Development</h4>
<ol>
<li><strong>Ultimate Service Fee</strong>: Development of tiering fee structure and automatic reversal</li>
<li><strong>Automated Reminder System</strong>: Implementation of billing and repayment reminders via email, SMS, Push notification (MyTSEL)</li>
<li><strong>Save Session Last Page</strong>: Implementation of session saving for the last page to prevent drop-offs</li>
<li><strong>Customer Lifecycle Enhancement</strong>: Various improvements</li>
</ol>
<h4 id="testing-schedule_1">Testing Schedule</h4>
<ul>
<li><strong>SIT</strong>: June 5 – June 11</li>
<li><strong>UAT</strong>: June 12 – June 25</li>
<li><strong>Regression</strong>: June 26 – July 2</li>
<li><strong>NFT-PT</strong>: June 26 – July 9</li>
<li><strong>NFT Security</strong>: June 26 – July 9</li>
<li><strong>NFT Security Remediation</strong>: July 2 – July 15</li>
</ul>
<h4 id="release-timeline">Release Timeline</h4>
<ul>
<li><strong>RFS</strong>: July 24</li>
<li><strong>FUT</strong>: July 25 – August 7</li>
<li><strong>RFC</strong>: August 11</li>
</ul>
<h2 id="action-items-priorities">Action Items &amp; Priorities</h2>
<h3 id="immediate-actions">Immediate Actions</h3>
<ul>
<li>Monitor and resolve transaction traffic anomaly between Splunk logs and metering database</li>
<li>Complete 2FA implementation on Digihub CMS</li>
<li>Implement connection prioritization and circuit breakers</li>
</ul>
<h3 id="medium-term-actions-rfs-may-27">Medium-term Actions (RFS May 27)</h3>
<ul>
<li>Develop automated reconciliation process</li>
<li>Refactor application architecture for better resilience</li>
<li>Set up connection pool monitoring and alerting</li>
</ul>
<h3 id="long-term-actions-rfs-june-11">Long-term Actions (RFS June 11)</h3>
<ul>
<li>Implement enhanced logging with correlation IDs</li>
<li>Develop reconciliation reports between Splunk and metering database</li>
<li>Implement comprehensive end-to-end transaction tracking</li>
<li>Establish continuous monitoring and improvement process</li>
<li>Consider database scaling or sharding for improved capacity</li>
</ul>
<h2 id="in-progress-items-15-items">In-Progress Items (15 items)</h2>
<h3 id="data-management">Data Management</h3>
<ol>
<li><strong>[RAID] Sync Data Reporting</strong>: Synchronization of Amartha data daily in Minio and status updates from KLOP to DFS Ops</li>
<li><strong>PIC</strong>: Amartha/Klop/DFS ops</li>
<li><strong>Latest Progress</strong>: Amartha informed it will be ready by June 6</li>
<li>
<p><strong>Current PIC</strong>: Amartha</p>
</li>
<li>
<p><strong>[RAID] Data Tracker</strong>: Implementation of data funnel tracker with raw data provision and ingestion to Hadoop</p>
</li>
<li><strong>PIC</strong>: Klop/BI</li>
<li><strong>Sample File Preparation</strong>: Klop Team to resend the sample file (with modification and filename) to BI for current development check</li>
</ol>
<hr />
<p><em>Report compiled by: Yuda</em><br />
<em>Date: Week 3 June (June 20, 2025)</em><br />
<em>Project: DIGIHUB</em></p>
        </div>
        <div class="footer">
            <div class="print-only">Document generated from Markdown</div>
            <div>Converted from Markdown to HTML • Yuda_Weekly_Report_W3_Jun_20.md</div>
        </div>
    </div>
</body>
</html>