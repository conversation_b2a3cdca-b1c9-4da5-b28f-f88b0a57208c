# Weekly Summary Report - June 2025
**Reporting Period:** Week 3 June (June 20-26, 2025)  
**Projects:** Telkomsel SmartPay & DIGIHUB  
**Compiled from:** PMC SmartPay Update & <PERSON><PERSON>'s Weekly Report

---

## Executive Summary

This consolidated report summarizes the status of two major Telkomsel projects during the fourth week of June 2025. Both projects are in critical phases with multiple testing activities and development milestones approaching.

### Key Highlights:
- **SmartPay**: Sprint 2 UAT completed, moving to regression testing
- **DIGIHUB**: Sprint 11 planning phase with 2FA implementation in progress
- **Critical Issues**: User registration drop (71.88%) and data synchronization challenges
- **Major Milestones**: Multiple G2/G3 gates approaching in July-August

---

## 📊 Project Status Overview

| Project | Current Phase | Status | Key Milestone | Risk Level |
|---------|---------------|--------|---------------|------------|
| **SmartPay** | Sprint 2 Testing | 🟢 On Track | G3-Sprint 2 (July 21) | 🟡 Medium |
| **DIGIHUB** | Sprint 11 Planning | 🟢 On Track | RFS (August 28-29) | 🟡 Medium |

---

## 🚀 SmartPay Project Update

### Current Status (June 26, 2025)
- **Phase**: Sprint 2 Testing & Sprint 3 Initiation
- **Overall Status**: 🟢 In Progress (On-Track)
- **UAT**: ✅ Completed (June 12-25)
- **Current Activities**: Regression, NFT-PT, NFT-Security testing

### Sprint 2 Achievements
**Development Features Completed (8/18 items):**

1. ✅ Campaign - Automated notification for user registration drop-off
2. ✅ Outstanding debt handling for recycled MSISDN
3. ✅ Save session last page
4. ✅ Share Personal Data-Image File via API (Jun 26 - RFS via standard change)
5. ✅ Service Fee Tiering
6. ✅ Automated reminder system (email, SMS, push notifications)
7. ✅ Customer Lifecycle Enhancement
8. ✅ Liveness (BR phase)

**In Progress (10/18 items):**

- 📅 SmartPay Dashboard (design phase)
- 📅 API Refund Handling
- 📅 Callback Repayment
- 📅 Various MyTsel integrations

**Operational Issue:**

- ✅ Automation for data funnel ingestion (Deployed Jun 25)
- 🔄 OCR Improvement
- 🔄 Intermittent 410
- 🔄 Gojek redirect URL

### Critical Performance Issue
**User Registration Decline:**

- **April Peak**: 30,027 registered users
- **May Drop**: 8,317 users (-71.88%)
- **June Trend**: 1,406 users (June 1-12)
- **RCA**: Due to drop of msisdn whitelist

### Upcoming Milestones
- **G3-Sprint 2**: July 21, 2025
- **RFS**: July 24, 2025
- **Sprint 3 G2**: August 1, 2025

---

## 🔧 DIGIHUB Project Update

### Current Status (June 20, 2025)
- **Phase**: Sprint 11 Development
- **Focus**: Settlement Automation & 2FA Implementation
- **Team Allocation**: 13.92 days average per assignee

### Sprint 11 Detailed Schedule
**Duration:** June 19 - September 19, 2025 (92 days)

| Phase | Activity | Start Date | End Date | Duration | Status |
|-------|----------|------------|----------|----------|--------|
| **Planning** | Sprint Planning 11 | 2025-06-24 | 2025-06-24 | 1 day | Completed |
| **Development** | 2FA Implementation | 2025-06-25 | 2025-07-18 | 24 days | In Progress |
| **Development** | UI/UX Checking | 2025-07-21 | 2025-07-21 | 1 day | Not Started |
| **Development** | Settlement Automation | 2025-06-18 | 2025-06-25 | 8 days | Not Started |
| **Testing** | SIT | 2025-07-22 | 2025-07-28 | 7 days | Not Started |
| **Testing** | UAT & Regression | 2025-07-29 | 2025-08-07 | 10 days | Not Started |
| **Testing** | NFT | 2025-08-08 | 2025-08-21 | 14 days | Not Started |
| **Testing** | VA & Pentest | 2025-08-08 | 2025-08-22 | 15 days | Not Started |
| **Testing** | Remediation & Retest | 2025-08-25 | 2025-08-27 | 3 days | Not Started |
| **Deployment** | RFS | 2025-08-28 | 2025-08-29 | 2 days | Not Started |
| **Deployment** | FUT | 2025-09-03 | 2025-09-17 | 15 days | Not Started |
| **Deployment** | RFC | 2025-09-18 | 2025-09-19 | 2 days | Not Started |

### Key Operational Activities
1. **2FA Implementation**: Digihub CMS (June 18)
2. **WAF Assessment**: Policy evaluation in progress
3. **Critical Issue**: Transaction traffic anomaly between Splunk logs and metering database

### Team Workload Distribution
- **Backend (Hanapi)**: 16.25 days (+16.76% above average)
- **Frontend (Fanny)**: 13.75 days (-1.20% below average)
- **Backend (Fatur)**: 11.75 days (-15.57% below average)

### Gate 2 Documents Timeline
- **SRS**: June 19 - July 17, 2025
- **SIT Test Cases**: June 19 - July 17, 2025 (±70 TCs)
- **API List for NFT**: July 16-17, 2025

---

## 🏗️ SmartPay Klop Infrastructure Analysis

### Infrastructure Migration Overview

**Project:** SmartPay Klop Infrastructure Migration
**Objective:** Migrate from preprod cluster to production cluster
**Urgency:** Critical - Current infrastructure at capacity limits

### Current Infrastructure Challenges
**Critical Performance Issues:**

- **CPU Utilization:** 8-9% (acceptable)
- **Memory Utilization:** 58-61% average, >70% on specific instances
- **Disk Utilization:** >70-80% on critical partitions (CRITICAL)
- **Customer Load:** 1,195 current customers, targeting 10 million (350x growth)
- **Resource Sharing:** Node shared with Cascade, POTLOC, and Digihub

### Proposed Infrastructure Architecture

#### Production Environment Specifications:
| Component | Current Issue | Proposed Solution |
|-----------|---------------|-------------------|
| **Backend Nodes (TBS & BSD)** | Resource contention | 2x Nodes (8 vCPU, 8GB RAM each) |
| **Frontend Nodes (TBS & BSD)** | Performance issues | 2x Nodes (8 vCPU, 8GB RAM each) |
| **MinIO Storage** | Disk space critical | 25GB dedicated storage |
| **Redis Memory** | Memory saturation | 1GB dedicated caching |
| **PostgreSQL** | Connection limits | 380 connections, 500GB storage |

#### Migration Strategy:

1. **Phase 1: Preparation** - Infrastructure setup, system audit
2. **Phase 2: Data Migration** - Strategic data transfer & validation
3. **Phase 3: Application Deployment** - Klop app deployment to new cluster
4. **Phase 4: Testing & Validation** - Functional, performance, security testing
5. **Phase 5: Cutover & Monitoring** - Traffic redirection, post-migration monitoring

### Infrastructure Impact Assessment
**Immediate Risks:**

- **Service Disruption:** High risk due to 70-80% disk utilization
- **Performance Degradation:** Memory saturation affecting user experience
- **Scalability Barrier:** Cannot support planned 10M customer growth

**Migration Benefits:**

- **Resource Liberation:** Will free up shared resources for other projects
- **Performance Improvement:** Dedicated infrastructure for SmartPay
- **Scalability:** Support for 350x customer growth

---

## ⚠️ Critical Issues & RAID Analysis

### High Priority Risks

#### 1. **Data Synchronization (SmartPay)**
- **Issue**: Amartha-KLOP-DFS Ops data flow challenges
- **Status**: Monitoring until June 26
- **Impact**: Operational reporting delays

#### 2. **User Registration Decline (SmartPay)**
- **Issue**: 71.88% drop in May registrations
- **Root Cause**: Under investigation
- **Impact**: Business objectives at risk

#### 3. **Transaction Traffic Anomaly (DIGIHUB)**
- **Issue**: Splunk logs vs metering database discrepancies
- **Identified**: June 26, 2025 09:31
- **Priority**: Immediate resolution required

#### 4. **Security Compliance (Both Projects)**
- **Issue**: ICT compliance requirements for image file sharing
- **Impact**: Potential delays in security testing phases

#### 5. **SmartPay Infrastructure Crisis**
- **Issue**: Critical infrastructure capacity (70-80% disk utilization)
- **Impact**: Immediate risk of service disruption
- **Priority**: Urgent migration required

### Medium Priority Issues
1. **Refresh Token Architecture**: Solution on hold pending review
2. **Dashboard Design**: SmartPay dashboard design phase delays
3. **Testing Resource Allocation**: Concurrent NFT phases pressure
4. **Cross-Project Resource Dependencies**: Potential DevOps resource contention between DIGIHUB and SmartPay infrastructure migration

---

## 📅 Upcoming Critical Milestones

### July 2025
| Date | Project | Milestone | Status |
|------|---------|-----------|--------|
| July 2-15 | SmartPay | NFT-Security Remediation | 📅 Scheduled |
| July 7 | DIGIHUB | Sprint 12 Planning | 📅 Scheduled |
| July 17-18 | DIGIHUB | UI/UX Checking | 📅 Scheduled |
| July 21 | SmartPay | G3-Sprint 2 | 🎯 Critical |
| July 24 | SmartPay | RFS | 🎯 Critical |

### August 2025
| Date | Project | Milestone | Status |
|------|---------|-----------|--------|
| Aug 1 | SmartPay | G2-Sprint 3 | 🎯 Critical |
| Aug 8-19 | DIGIHUB | NFT Testing | 📅 Scheduled |
| Aug 11-25 | DIGIHUB | Penetration Testing | 📅 Scheduled |

---

## 🎯 Action Items & Recommendations

### Immediate Actions (Next Week)
**SmartPay:**

- [ ] Complete regression testing phase
- [ ] Investigate user registration decline root cause
- [ ] Resolve data synchronization monitoring
- [ ] Finalize SmartPay Dashboard design

**DIGIHUB:**

- [ ] Resolve transaction traffic anomaly
- [ ] Complete 2FA implementation
- [ ] Prepare Gate 2 documentation

**SmartPay Infrastructure:**

- [ ] Begin infrastructure migration Phase 1 immediately
- [ ] Address critical disk space utilization (>80%)
- [ ] Plan resource allocation for migration activities
- [ ] Coordinate with DIGIHUB team on shared resources

### Strategic Recommendations

#### 1. **Resource Optimization**
- Consider additional testing resources for concurrent NFT phases
- Reallocate team members based on workload analysis
- Implement cross-project knowledge sharing

#### 2. **Risk Mitigation**
- Develop contingency plans for integration dependencies
- Establish alternative approaches for token management
- Create escalation procedures for critical issues

#### 3. **Performance Improvement**
- Implement comprehensive monitoring for user registration trends
- Enhance data quality processes for OCR-related issues
- Establish automated alerting for anomaly detection

#### 4. **Communication Enhancement**
- Weekly cross-project sync meetings
- Stakeholder updates on critical milestone progress
- Escalation protocols for high-priority issues

#### 5. **Infrastructure Coordination**
- Establish cross-project resource allocation matrix
- Coordinate SmartPay migration timeline with DIGIHUB Sprint 11
- Implement knowledge sharing from SmartPay infrastructure lessons
- Ensure DIGIHUB environment isolation during SmartPay migration

---

## 📈 Success Metrics & KPIs

### SmartPay Performance
- **Feature Completion**: 47% (9/19 Sprint 2 items)
- **User Approval Rate**: >89% (consistently high)
- **Testing Progress**: UAT completed, 3 concurrent testing phases active

### DIGIHUB Performance
- **Sprint Planning**: On schedule
- **Team Utilization**: Balanced across frontend/backend
- **Documentation**: Gate 2 documents on track

---

## 🔮 Looking Ahead

### Next Week Priorities (July 1-7)
1. **SmartPay G3-Sprint 2 preparation**
2. **DIGIHUB Sprint 12 planning session**
3. **User registration decline investigation results**
4. **Security compliance requirements resolution**
5. **SmartPay infrastructure migration Phase 1 initiation**
6. **Cross-project resource coordination setup**

### Month-End Targets (July 31)
1. **SmartPay RFS achievement**
2. **DIGIHUB Sprint 11 development completion**
3. **All critical RAID items resolution**
4. **Sprint 3 readiness confirmation**
5. **SmartPay infrastructure migration Phase 2-3 completion**
6. **Infrastructure capacity issues resolution**

---

*Report compiled by: Project Management Office*
*Sources: PMC SmartPay Update (June 26), Yuda Weekly Report (June 20), DIGIHUB Sprint 11 Timeline v.4.xlsx & SmartPay Infrastructure Proposal*
*Next update: July 3, 2025*
