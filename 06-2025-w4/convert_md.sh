#!/bin/bash
# Markdown to HTML Converter - Batch Script
# Usage: ./convert_md.sh [options] file1.md file2.md ...

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
OUTPUT_DIR=""
BATCH_MODE=false
OPEN_BROWSER=false

# Function to display usage
usage() {
    echo -e "${BLUE}Markdown to HTML Converter${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS] file1.md [file2.md ...]"
    echo ""
    echo "Options:"
    echo "  -o, --output-dir DIR    Output directory for HTML files"
    echo "  -b, --batch            Enable batch mode"
    echo "  -w, --open             Open generated HTML in browser"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 report.md                           # Convert single file"
    echo "  $0 -o html_output *.md                 # Convert all .md files to html_output/"
    echo "  $0 -b -w *.md                          # Batch convert and open in browser"
    echo ""
}

# Function to check dependencies
check_dependencies() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}Error: Python 3 is required but not installed.${NC}"
        exit 1
    fi
    
    if ! python3 -c "import markdown" 2>/dev/null; then
        echo -e "${YELLOW}Installing required Python packages...${NC}"
        pip3 install markdown
    fi
}

# Function to open file in browser
open_in_browser() {
    local file="$1"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        open "$file"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        xdg-open "$file"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        # Windows
        start "$file"
    else
        echo -e "${YELLOW}Cannot auto-open browser on this system. Please open: $file${NC}"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -b|--batch)
            BATCH_MODE=true
            shift
            ;;
        -w|--open)
            OPEN_BROWSER=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        -*)
            echo -e "${RED}Unknown option: $1${NC}"
            usage
            exit 1
            ;;
        *)
            break
            ;;
    esac
done

# Check if files are provided
if [[ $# -eq 0 ]]; then
    echo -e "${RED}Error: No input files specified.${NC}"
    usage
    exit 1
fi

# Check dependencies
check_dependencies

# Prepare Python script arguments
PYTHON_ARGS=()
if [[ "$BATCH_MODE" == true ]] || [[ $# -gt 1 ]]; then
    PYTHON_ARGS+=("--batch")
fi

if [[ -n "$OUTPUT_DIR" ]]; then
    PYTHON_ARGS+=("--output-dir" "$OUTPUT_DIR")
    mkdir -p "$OUTPUT_DIR"
fi

# Add input files
PYTHON_ARGS+=("$@")

echo -e "${BLUE}Converting markdown files to HTML...${NC}"
echo ""

# Run the Python converter
if python3 markdown_to_html.py "${PYTHON_ARGS[@]}"; then
    echo ""
    echo -e "${GREEN}✅ Conversion completed successfully!${NC}"
    
    # Open in browser if requested
    if [[ "$OPEN_BROWSER" == true ]]; then
        echo -e "${BLUE}Opening HTML files in browser...${NC}"
        for file in "$@"; do
            if [[ -f "$file" ]]; then
                if [[ -n "$OUTPUT_DIR" ]]; then
                    html_file="$OUTPUT_DIR/$(basename "${file%.*}.html")"
                else
                    html_file="${file%.*}.html"
                fi
                
                if [[ -f "$html_file" ]]; then
                    open_in_browser "$html_file"
                fi
            fi
        done
    fi
else
    echo -e "${RED}❌ Conversion failed!${NC}"
    exit 1
fi
