# Pandoc Document Converters

Professional document conversion tools using Pandoc - the universal document converter. These scripts provide high-quality conversion from Markdown to HTML, Word (DOCX), PDF, and other formats with professional styling and formatting.

## Why Pandoc?

You're absolutely right to ask about Pandoc! Here's why it's superior to custom solutions:

✅ **Industry Standard**: Pandoc is the gold standard for document conversion  
✅ **Format Support**: 40+ input/output formats including HTML, DOCX, PDF, LaTeX, EPUB  
✅ **Professional Quality**: Better handling of complex documents, tables, citations  
✅ **Extensible**: Custom templates, filters, and styling options  
✅ **Reliable**: Battle-tested by academics, publishers, and enterprises worldwide  
✅ **Active Development**: Continuously updated with new features and bug fixes  

## Features

🎨 **Professional Styling**
- Custom HTML templates with modern, responsive design
- Professional Word document formatting with custom styles
- PDF generation with LaTeX quality typesetting

📋 **Advanced Document Features**
- Automatic table of contents generation
- Syntax highlighting for code blocks
- Professional table formatting
- Cross-references and citations support

📊 **Multiple Output Formats**
- **HTML**: Responsive web documents with professional styling
- **DOCX**: Microsoft Word with custom formatting and styles
- **PDF**: High-quality PDF via LaTeX (requires LaTeX installation)
- **ODT**: OpenDocument Text format
- **RTF**: Rich Text Format for universal compatibility

🔧 **Flexible Usage**
- Single file conversion
- Batch processing
- Custom templates and styling
- Command-line and Python interfaces

## Installation

### Prerequisites

**1. Install Pandoc**
```bash
# macOS
brew install pandoc

# Ubuntu/Debian
sudo apt-get install pandoc

# Windows
# Download from https://pandoc.org/installing.html
```

**2. Install Python Dependencies**
```bash
pip install -r requirements.txt
# or
pip install python-docx
```

**3. For PDF Generation (Optional)**
```bash
# macOS
brew install --cask mactex

# Ubuntu/Debian
sudo apt-get install texlive-full
```

### Verify Installation
```bash
pandoc --version
python3 -c "import docx; print('python-docx installed')"
```

## Usage

### 🚀 Quick Start

**Convert to HTML:**
```bash
./convert_with_pandoc.sh report.md
```

**Convert to Word:**
```bash
./convert_with_pandoc.sh -f docx report.md
```

**Convert to PDF:**
```bash
./convert_with_pandoc.sh -f pdf report.md
```

### 📁 Batch Conversion

**Convert all markdown files to Word:**
```bash
./convert_with_pandoc.sh -f docx -b *.md
```

**Convert to specific directory:**
```bash
./convert_with_pandoc.sh -f docx -o word_docs *.md
```

**Convert and open results:**
```bash
./convert_with_pandoc.sh -f docx -b -w *.md
```

### 🐍 Python Scripts

**General Pandoc converter:**
```bash
python3 pandoc_converter.py input.md --format html
python3 pandoc_converter.py input.md --format docx
python3 pandoc_converter.py --batch --format pdf *.md
```

**Specialized Word converter:**
```bash
python3 md_to_word.py report.md
python3 md_to_word.py --batch --output-dir word_docs *.md
```

## Command Reference

### Shell Script Options (`convert_with_pandoc.sh`)

| Option | Description | Example |
|--------|-------------|---------|
| `-f, --format` | Output format (html, docx, pdf, odt, rtf) | `-f docx` |
| `-o, --output-dir` | Output directory | `-o output_folder` |
| `-b, --batch` | Batch mode | `-b` |
| `-w, --open` | Open files after conversion | `-w` |
| `--no-toc` | Disable table of contents | `--no-toc` |
| `--highlight` | Code highlighting style | `--highlight github` |
| `--template` | Custom template file | `--template custom.html` |

### Python Script Options

**pandoc_converter.py:**
- `--format`: Output format
- `--batch`: Batch conversion
- `--output-dir`: Output directory
- `--pandoc-options`: Additional Pandoc options
- `--create-reference`: Create Word reference document

**md_to_word.py:**
- `--batch`: Batch conversion
- `--output-dir`: Output directory
- `--no-toc`: Disable table of contents
- `--no-enhance`: Skip document enhancement
- `--create-reference`: Create Word reference document

## Examples with Your Files

### Convert Your Project Reports

**Convert all reports to Word:**
```bash
./convert_with_pandoc.sh -f docx -o word_reports *.md
```

**Convert specific files:**
```bash
./convert_with_pandoc.sh -f docx \
  PMC_SmartPay_Update_20250626.md \
  Yuda_Weekly_Report_W3_Jun_20.md \
  Weekly_Summary_Report_June_2025.md
```

**Create HTML versions for web sharing:**
```bash
./convert_with_pandoc.sh -f html -o html_reports -b *.md
```

**Generate PDFs for printing:**
```bash
./convert_with_pandoc.sh -f pdf -o pdf_reports *.md
```

### Professional Word Documents

**Create enhanced Word documents:**
```bash
python3 md_to_word.py --batch --output-dir professional_docs *.md
```

**Custom Word styling:**
```bash
# First create reference document
python3 md_to_word.py --create-reference

# Edit word_reference.docx in Word to customize styles
# Then convert with custom styling
python3 md_to_word.py --batch *.md
```

## Customization

### HTML Templates

Create custom HTML templates by modifying the template in `convert_with_pandoc.sh` or using external templates:

```bash
./convert_with_pandoc.sh --template my_template.html report.md
```

### Word Styling

1. Create reference document:
```bash
python3 md_to_word.py --create-reference
```

2. Open `word_reference.docx` in Microsoft Word
3. Modify styles (Heading 1, Heading 2, Normal, etc.)
4. Save the reference document
5. Convert documents - they'll use your custom styles

### PDF Styling

For PDF customization, you can:
- Use custom LaTeX templates
- Modify CSS for HTML-to-PDF conversion
- Add custom Pandoc variables

## Output Quality Comparison

| Feature | Custom Python | Pandoc |
|---------|---------------|--------|
| **Table Handling** | Basic | ✅ Advanced |
| **Code Highlighting** | Limited | ✅ 100+ languages |
| **Math Equations** | None | ✅ LaTeX support |
| **Citations** | None | ✅ BibTeX/CSL |
| **Cross-references** | None | ✅ Full support |
| **Format Fidelity** | Good | ✅ Excellent |
| **Maintenance** | Custom | ✅ Community |

## Troubleshooting

### Common Issues

**"pandoc: command not found"**
```bash
# Install Pandoc first
brew install pandoc  # macOS
sudo apt-get install pandoc  # Ubuntu
```

**PDF generation fails**
```bash
# Install LaTeX
brew install --cask mactex  # macOS
sudo apt-get install texlive-full  # Ubuntu
```

**Word documents lack styling**
```bash
# Create reference document
python3 md_to_word.py --create-reference
```

**Permission denied**
```bash
chmod +x convert_with_pandoc.sh
```

### Performance Tips

- Use batch mode for multiple files
- For large documents, consider splitting into sections
- PDF generation is slower than HTML/DOCX
- Use `--no-toc` for faster conversion if TOC not needed

## Advanced Usage

### Custom Pandoc Options

```bash
# Add custom Pandoc options
python3 pandoc_converter.py input.md --format html \
  --pandoc-options --css custom.css --self-contained
```

### Filters and Extensions

```bash
# Use Pandoc filters
pandoc input.md -o output.html --filter pandoc-citeproc
```

### Batch Processing with Custom Logic

```bash
# Process files with custom naming
for file in *.md; do
  ./convert_with_pandoc.sh -f docx "$file"
  mv "${file%.md}.docx" "FINAL_${file%.md}.docx"
done
```

## Integration

### CI/CD Pipeline

```yaml
# GitHub Actions example
- name: Convert Documentation
  run: |
    sudo apt-get install pandoc
    pip install python-docx
    ./convert_with_pandoc.sh -f html -o docs *.md
```

### Automated Workflows

```bash
# Watch for changes and auto-convert
fswatch -o *.md | xargs -n1 -I{} ./convert_with_pandoc.sh -f docx
```

## Why This Approach is Better

1. **Reliability**: Pandoc handles edge cases and complex formatting
2. **Standards Compliance**: Output follows document format specifications
3. **Extensibility**: Easy to add new formats and features
4. **Community Support**: Large user base and active development
5. **Professional Quality**: Used by publishers, academics, and enterprises
6. **Future-Proof**: Continuously updated with new features

---

**Recommendation**: Use these Pandoc-based converters instead of the custom Python solution for production use. They provide better quality, more features, and greater reliability.

🎉 **Happy Converting with Pandoc!**
