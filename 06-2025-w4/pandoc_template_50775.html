<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6; color: #333; background: #f8f9fa; padding: 20px;
        }
        .container {
            max-width: 1200px; margin: 0 auto; background: white;
            border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 30px; text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .content { padding: 40px; }
        h1, h2, h3, h4, h5, h6 { margin: 30px 0 15px 0; color: #2c3e50; }
        h1 { font-size: 2.2em; border-bottom: 3px solid #667eea; padding-bottom: 10px; }
        h2 { font-size: 1.8em; border-bottom: 2px solid #ddd; padding-bottom: 8px; }
        h3 { font-size: 1.5em; color: #667eea; }
        table {
            width: 100%; border-collapse: collapse; margin: 20px 0; background: white;
            border-radius: 8px; overflow: hidden; box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #667eea; color: white; font-weight: 600; }
        tr:hover { background: #f5f5f5; }
        code {
            background: #f4f4f4; padding: 2px 6px; border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace; color: #e74c3c;
        }
        pre {
            background: #2c3e50; color: #ecf0f1; padding: 20px;
            border-radius: 5px; overflow-x: auto; margin: 20px 0;
        }
        blockquote {
            border-left: 4px solid #667eea; margin: 20px 0; padding: 15px 20px;
            background: #f8f9fa; font-style: italic;
        }
        #TOC { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px; }
        #TOC h2 { color: #667eea; margin-bottom: 15px; }
        .footer {
            background: #2c3e50; color: white; padding: 20px;
            text-align: center; font-size: 0.9em;
        }
        @media (max-width: 768px) {
            body { padding: 10px; } .content { padding: 20px; }
            .header { padding: 20px; } .header h1 { font-size: 2em; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>$if(title)$$title$$else$Document$endif$</h1>
            <div class="meta">Generated on $date$</div>
        </div>
        <div class="content">
            $if(toc)$<nav id="TOC"><h2>📋 Table of Contents</h2>$toc$</nav>$endif$
            $body$
        </div>
        <div class="footer">
            <div>Generated with Pandoc • $(date '+%B %d, %Y at %I:%M %p')</div>
        </div>
    </div>
</body>
</html>
