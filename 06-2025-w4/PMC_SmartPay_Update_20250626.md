# PMC SmartPay Project Update
**Date:** June 26, 2025  
**Project:** Telkomsel SmartPay  
**Document Type:** Program Management Committee Update

---

## Executive Summary

This document presents the Program Management Committee update for the Telkomsel SmartPay project as of June 26, 2025. The project is currently in Sprint 2 phase with multiple workstreams progressing according to schedule.

---

## Table of Contents

1. [Program Schedule, Milestones & Key Management Highlights](#program-schedule)
2. [Workstream Update](#workstream-update)
3. [Risks, Actions, Issues, Dependencies (RAID)](#raid)
4. [Change Request](#change-request)
5. [Operation Update](#operation-update)

---

## 1. Program Schedule, Milestones & Key Management Highlights {#program-schedule}

### High-Level Project Timeline (2025)

#### Current Status: **We're here** (Week of June 26)

### Sprint 2 Timeline
- **Requirement Grooming**: April 21 - May 2
- **Sprint Planning**: May 5 - May 16
- **Development and UT**: May 19 - May 30
- **Testing Phase**: June 5 - July 15

#### Key Milestones Completed:
- ✅ **BR Doc**: May 2
- ✅ **LLD & IFA**: May 23
- ✅ **Design Solution**: May 16
- ✅ **G2-Sprint 2**: June 5

#### Upcoming Milestones:
- 🔄 **G3-Sprint 2**: July 21
- 📅 **RFS**: July 24

### Sprint 3 Timeline
- **Start Date**: June 26 - July 9
- **BR Doc**: June 27
- **Design Solution**: July 11
- **G2-Sprint 3**: August 1
- **G3-Sprint 3**: September 17

### Testing Schedule

#### Current Testing Phase:
- **SIT**: June 5 - June 11 ✅
- **UAT**: June 12 - June 25 ✅
- **Regression**: June 26 - July 2 🔄
- **NFT-PT**: June 26 - July 9 🔄
- **NFT-Security**: June 26 - July 9 🔄
- **NFT-Security Remediation**: July 2 - July 15 📅

#### Future Testing:
- **RFS KLOP**: July 25 - August 7
- **FUT**: July 25 - August 7
- **RFC**: August 11

---

## 2. Workstream Update {#workstream-update}

### SmartPay Sprint 2 Status

#### Progress Overview:
- **Overall Status**: 🟢 In Progress (On-Track)
- **UAT Progress**: Currently in execution phase

### Sprint 2 Backlog Items

| No | Feature | Priority | Impacted App | Surrounding | BR Status | Design Status |
|----|---------|----------|--------------|-------------|-----------|---------------|
| 1 | Campaign - Automated notification for user registration drop off | High | Klop | - | ✅ Completed | ✅ Completed |
| 2 | Outstanding debt handling for recycled MSISDN | High | Klop | UPS | ✅ Completed | ✅ Completed |
| 3 | Save session last page | High | Klop | - | ✅ Completed | ✅ Completed |
| 4 | Share Personal Data-Image File via API | High | Klop | - | ✅ Completed | ✅ Completed |
| 5 | Service Fee Tiering | High | Amartha, Klop | - | ✅ Completed | ✅ Completed |
| 6 | Automated reminder for billing, due, past due (via email, SMS, push notif MyTsel) | High | Amartha, Klop | NOIS | ✅ Completed | ✅ Completed |
| 7 | Customer Lifecycle Enhancement | High | Klop | - | ✅ Completed | ✅ Completed |
| 8 | SmartPay Dashboard | High | - | - | ✅ Completed | 🔄 In Progress |
| 9 | Telkomsel SmartPay di UPP (BR Existing) | High | UPP, Klop | MyTsel | ✅ Completed | ✅ Completed |
| 10 | API Refund Handling | High | Klop | Amartha | 🔄 In Progress | 🔄 In Progress |
| 11 | Callback Repayment | High | Klop | Amartha | 🔄 In Progress | 🔄 In Progress |
| 12 | Liveness (BR Existing) | High | Klop/Amartha | - | ✅ Completed | 🔄 In Progress |
| 13 | Tracker/counter for page keuangan in MyTsel | High | MyTsel | - | 🔄 In Progress | 🔄 In Progress |
| 14 | Parameter campaign banner (tracker campaign) | High | MyTsel | - | 🔄 In Progress | 🔄 In Progress |
| 15 | Automated reminder for Billing, due-date, past-due (via WA) | High | KLOP | NOIS | 🔄 In Progress | 🔄 In Progress |
| 16 | [IT Internal] User token negative case handling | High | KLOP | - | N/A | 🔄 In Progress |
| 17 | Subscriber blocking | High | - | - | - | - |
| 18 | Installment Use Case | High | Amartha | - | 🔄 In Progress | 🔄 In Progress |
| 19 | Cash Loan Use Case | High | Amartha | - | 🔄 In Progress | 🔄 In Progress |

### Key Features Delivered

#### 1. **Campaign - Automated Notification System**
- **Scope**: User registration drop-off notifications
- **Impact**: Improved user conversion rates
- **Status**: Ready for testing

#### 2. **Outstanding Debt Handling**
- **Scope**: Recycled MSISDN debt management
- **Integration**: UPS system integration
- **Status**: Implementation complete

#### 3. **Service Fee Tiering**
- **Scope**: Dynamic fee structure implementation
- **Applications**: Amartha & Klop integration
- **Status**: Ready for deployment

#### 4. **Automated Reminder System**
- **Channels**: Email, SMS, Push notifications (MyTsel)
- **Integration**: NOIS system
- **Scope**: Billing, due dates, past due notifications

#### 5. **Customer Lifecycle Enhancement**
- **Scope**: End-to-end customer experience improvements
- **Status**: Development completed

### Amartha Product Backlog

| No | Feature | Priority | Stakeholder | BRD/PRD | Development | IFA | Notes |
|----|---------|----------|-------------|---------|-------------|-----|-------|
| 1 | Service Fee Tiering | High | Klop | ✅ Completed | ✅ Completed | ✅ Completed | Done - Waiting for Tsel Development |
| 2 | Dukcapil Validation | High | Klop | ✅ Completed | 🔄 In Progress | ⏸️ Not Started | ETA Release June 11, 2025 |
| 4 | API Refund Handling | High | Klop/Elisa | ✅ Completed | ✅ Completed | ✅ Completed | Done - Waiting for Tsel Development |
| 5 | Callback Repayment | High | Klop/Elisa | ✅ Completed | ✅ Completed | ✅ Completed | Done - Waiting for Tsel Development |
| 6 | Liveness | Medium | - | 🔄 In Progress | ⏸️ Not Started | ⏸️ Not Started | Waiting for API Onboarding |
| 7 | [Improvement] Refund Automation | Low | - | 🔄 In Progress | ⏸️ Not Started | ⏸️ Not Started | Waiting for API Refund Handling |
| 8 | OCR Improvement | Medium | - | 🔄 In Progress | ⏸️ Not Started | ⏸️ Not Started | Need to discuss with Telkomsel |
| 9 | Face Match | High | - | 🔄 In Progress | ⏸️ Not Started | ⏸️ Not Started | Waiting for API Onboarding |
| 10 | Image Anti Tampering | High | - | 🔄 In Progress | ⏸️ Not Started | ⏸️ Not Started | Waiting for API Onboarding |
| 11 | API Onboarding | High | Klop | ✅ Completed | ✅ Completed | ✅ Completed | Done - Waiting for Tsel Development |
| 12 | Underwriting | High | Klop, Data | ⏸️ Not Started | ⏸️ Not Started | ⏸️ Not Started | Waiting for API Onboarding |

### Amartha Sprint Backlog

| No | Feature | Priority | Stakeholder | Development Status | IFA | Note |
|----|---------|----------|-------------|-------------------|-----|------|
| 1 | Event tracker repayment | High | - | 🔄 In Progress | No Need | - |
| 2 | Dukcapil validation | High | Klop | 🔄 In Progress | No Need | - |
| 3 | Payment option reorder | Medium | - | 🔄 In Progress | No Need | - |
| 4 | Tech debt billing | High | - | 🔄 In Progress | No Need | - |
| 5 | Improvement Dana & Gopay | High | - | 🔄 In Progress | No Need | - |

---

## 3. Risks, Actions, Issues, Dependencies (RAID) {#raid}

### Key RAID Items

| No. | Category | Type | Backlog | Details | Current PIC | Due Date | Status |
|-----|----------|------|---------|---------|-------------|----------|--------|
| 1 | Action | BAU | Sync Data Reporting | Amartha provide daily data in Minio, KLOP used and enrich the data to DFS Ops | DFS OPS (Fikhri) | June 26 | 🔄 In Progress |
| 4 | Action | Development | Refresh Token | IT EA team raised concerns regarding the Refresh Token Solution | PMO (Erwin) | TBD | ⏸️ On Hold |
| 14 | Action | BAU | Manual Image File Sharing | Mechanism of sharing manual image file confirmation from Amartha | L1 Bram, PMO (Hary) | TBD | 🔄 In Progress |

### Detailed RAID Analysis

#### 1. **Sync Data Reporting** (In Progress)
- **Issue**: Amartha needs to provide daily data in Minio for KLOP to enrich and send to DFS Ops
- **Timeline Updates**:
  - [15 Apr] Data available up to April 14
  - [23 Apr] DFS Ops confirmed data received but timing not as expected
  - [2 May] Extended development needed for different data sources
  - [15 May] Amartha informed ready by June 6
  - [27 May] Semi-automated delivery on June 5
  - [11 Jun] Configuration update on min.io
  - [18 Jun] Data sent, DFS ops requested June 4-12 files separately
  - [19 Jun] DFS ops confirmed OK, monitoring until June 26
- **Current Status**: Monitoring phase until June 26

#### 2. **Refresh Token Solution** (On Hold)
- **Issue**: IT EA team raised concerns about current refresh token solution
- **Impact**: Solution will not address possible token problems on Amartha side
- **Decision**: Aligned with ITSA - current refresh solution not needed
- **Status**: On hold pending further review

#### 3. **Manual Image File Sharing** (In Progress)
- **Issue**: Need mechanism for sharing manual image file confirmation from Amartha
- **Progress**:
  - [22 May] Data requested by event, SOP request informed May 23
  - [23 May] Amartha confirmation email sent
  - [26 May] Daily delivery expectation, tower team assessment
  - [28 May] Delivered to L1 team
  - [3 Jun] L1 requested Security Clearance from IT Sec
  - [11 Jun] ICT compliance requirements identified
- **Current Status**: Awaiting compliance requirements fulfillment

### Current Risk Assessment

#### 🔴 High Priority Risks
1. **Data Synchronization Dependencies**
   - Amartha data delivery timing issues
   - DFS Ops integration dependencies

2. **Security Compliance Requirements**
   - ICT compliance requirements for manual image file sharing
   - Security clearance processes causing delays

3. **Testing Timeline Pressure**
   - Concurrent testing phases (NFT-PT, NFT-Security)
   - Resource allocation challenges

#### 🟡 Medium Priority Issues
1. **SmartPay Dashboard Design**
   - Design phase still in progress
   - Potential impact on Sprint 2 completion

2. **Token Management Architecture**
   - Refresh token solution on hold
   - Need alternative approach for token handling

### Action Items

#### Immediate Actions (This Week)
- [ ] Monitor Amartha data sync until June 26
- [ ] Complete UAT execution for all Sprint 2 features
- [ ] Initiate Regression testing phase
- [ ] Begin NFT-PT and NFT-Security testing
- [ ] Finalize SmartPay Dashboard design

#### Short-term Actions (Next 2 Weeks)
- [ ] Resolve ICT compliance requirements for image file sharing
- [ ] Complete all Sprint 2 testing phases
- [ ] Prepare for G3-Sprint 2 milestone
- [ ] Review refresh token solution alternatives
- [ ] Initiate Sprint 3 requirement grooming

---

## 4. Change Request {#change-request}

### Pending Change Requests

*[This section would contain any formal change requests - content not fully visible in the extracted text]*

### Approved Changes

*[Details of approved changes would be listed here]*

---

## 5. Operation Update {#operation-update}

### Operational Issues (Period: June 1-11, 2025)

| No | Issue Type | Root Cause | Action | Raise Date | Plan | Actual | Severity | Status | PIC |
|----|------------|------------|--------|------------|------|--------|----------|--------|-----|
| 1 | Anomaly Data Issue | Data captured through OCR and submitted directly by user | • Track end-to-end process<br>• List investigation results<br>• Suggest solution | June 4 | June 4-11 | June 4-11 | High | ✅ Completed | Avian/Dodi |
| 2 | Pending Support Refresh Token | Amartha internal incident caused postponement | • Waiting for next schedule confirmation | June 10 | June 11 | TBC | High | ⏸️ Not Started | Avian/Dodi |

### KLOP Performance Analysis (January - June 12, 2025)

#### User Registration Trends

**Key Highlights:**
- **Peak Performance**: April 2025 with highest user registration traffic
- **Significant Drop**: 71.88% decrease in May compared to April
- **Current Status**: June showing continued low registration numbers

#### Monthly Registration Data:

| Month | Registered Users | Rejected Users | Total Applications |
|-------|------------------|----------------|-------------------|
| January | 194 | 13 | 207 |
| February | 823 | 60 | 883 |
| March | 24,967 | 1,904 | 26,877 |
| April | 30,027 | 2,921 | 32,955 |
| May | 8,317 | 898 | 9,263 |
| June (1-12) | 1,168 | 190 | 1,376 |

#### Performance Analysis:
- **REGISTERED**: Users approved by Amartha who became Telkomsel SmartPay customers
- **REJECTED**: Users rejected by Amartha during the approval process
- **Approval Rate**: Consistently high across all months (>89%)
- **Concern**: Dramatic drop in May requires investigation and corrective action

### Current Operational Status

#### System Performance
- **Availability**: Monitoring in progress
- **Performance Metrics**: Within acceptable thresholds for approved users
- **User Adoption**: Significant decline requiring immediate attention

#### Support & Maintenance
- **Issue Resolution**:
  - 1 critical issue resolved (Anomaly Data Issue)
  - 1 pending issue (Refresh Token Support)
- **User Support**: Help desk operational
- **Documentation**: Updated for new features

#### Immediate Operational Concerns
1. **User Registration Drop**: 71.88% decrease in May needs root cause analysis
2. **Refresh Token Support**: Pending resolution due to Amartha internal issues
3. **Data Quality**: OCR-related anomalies requiring process improvements

---

## Next Steps & Recommendations

### Immediate Focus Areas (Next 7 Days)
1. **Complete UAT Phase**: Ensure all test cases are executed successfully
2. **Regression Testing**: Begin comprehensive regression testing
3. **Security Testing**: Initiate NFT-Security testing phase
4. **Design Completion**: Finalize pending design work for Dashboard and UPP

### Strategic Recommendations
1. **Resource Allocation**: Consider additional testing resources for concurrent NFT phases
2. **Risk Mitigation**: Develop contingency plans for integration dependencies
3. **Communication**: Enhance stakeholder communication for Sprint 3 preparation
4. **Quality Assurance**: Implement additional quality gates before G3-Sprint 2

---

## Appendix

### Legend
- ✅ **Completed**: Task/milestone successfully finished
- 🔄 **In Progress**: Currently being worked on
- 📅 **Scheduled**: Planned for future execution
- 🔴 **High Risk**: Requires immediate attention
- 🟡 **Medium Risk**: Monitor closely
- 🟢 **On Track**: Progressing as planned

### Contact Information
- **Project Manager**: [To be filled]
- **Technical Lead**: [To be filled]
- **Business Owner**: [To be filled]

---

*Document prepared by: PMC Team*  
*Last updated: June 26, 2025*  
*Next update: July 3, 2025*
