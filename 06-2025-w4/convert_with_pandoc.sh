#!/bin/bash
# Pandoc Document Converter - Professional document conversion script
# Supports HTML, Word (DOCX), PDF, and other formats

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default values
OUTPUT_DIR=""
OUTPUT_FORMAT="html"
BATCH_MODE=false
OPEN_BROWSER=false
CREATE_TOC=true
HIGHLIGHT_STYLE="github"
TEMPLATE=""

# Function to display usage
usage() {
    echo -e "${BLUE}Pandoc Document Converter${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS] file1.md [file2.md ...]"
    echo ""
    echo "Options:"
    echo "  -f, --format FORMAT     Output format: html, docx, pdf, odt, rtf (default: html)"
    echo "  -o, --output-dir DIR    Output directory for converted files"
    echo "  -b, --batch            Enable batch mode"
    echo "  -w, --open             Open generated files after conversion"
    echo "  --no-toc               Disable table of contents"
    echo "  --highlight STYLE      Code highlighting style (default: github)"
    echo "  --template FILE        Custom template file"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Supported formats:"
    echo "  html    - HTML with professional styling"
    echo "  docx    - Microsoft Word document"
    echo "  pdf     - PDF document (requires LaTeX)"
    echo "  odt     - OpenDocument Text"
    echo "  rtf     - Rich Text Format"
    echo ""
    echo "Examples:"
    echo "  $0 report.md                              # Convert to HTML"
    echo "  $0 -f docx report.md                      # Convert to Word"
    echo "  $0 -f pdf -o pdf_output *.md              # Convert all to PDF"
    echo "  $0 -b -f docx -w *.md                     # Batch convert to Word and open"
    echo ""
}

# Function to check dependencies
check_dependencies() {
    echo -e "${CYAN}🔧 Checking dependencies...${NC}"
    
    # Check Pandoc
    if ! command -v pandoc &> /dev/null; then
        echo -e "${RED}❌ Error: Pandoc is not installed.${NC}"
        echo ""
        echo "To install Pandoc:"
        echo "  macOS:   brew install pandoc"
        echo "  Ubuntu:  sudo apt-get install pandoc"
        echo "  Windows: Download from https://pandoc.org/installing.html"
        echo ""
        exit 1
    fi
    
    # Get Pandoc version
    PANDOC_VERSION=$(pandoc --version | head -n1)
    echo -e "${GREEN}✅ $PANDOC_VERSION${NC}"
    
    # Check format-specific dependencies
    case $OUTPUT_FORMAT in
        pdf)
            if ! command -v xelatex &> /dev/null && ! command -v pdflatex &> /dev/null; then
                echo -e "${YELLOW}⚠️  Warning: LaTeX not found. PDF conversion may fail.${NC}"
                echo "  Install LaTeX: brew install --cask mactex (macOS) or sudo apt-get install texlive (Ubuntu)"
            fi
            ;;
        docx)
            if command -v python3 &> /dev/null; then
                if ! python3 -c "import docx" 2>/dev/null; then
                    echo -e "${YELLOW}⚠️  Installing python-docx for better Word styling...${NC}"
                    pip3 install python-docx 2>/dev/null || echo -e "${YELLOW}   (Optional: pip3 install python-docx)${NC}"
                fi
            fi
            ;;
    esac
}

# Function to create HTML template
create_html_template() {
    local template_file="pandoc_template_$$.html"
    cat > "$template_file" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6; color: #333; background: #f8f9fa; padding: 20px;
        }
        .container {
            max-width: 1200px; margin: 0 auto; background: white;
            border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 30px; text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .content { padding: 40px; }
        h1, h2, h3, h4, h5, h6 { margin: 30px 0 15px 0; color: #2c3e50; }
        h1 { font-size: 2.2em; border-bottom: 3px solid #667eea; padding-bottom: 10px; }
        h2 { font-size: 1.8em; border-bottom: 2px solid #ddd; padding-bottom: 8px; }
        h3 { font-size: 1.5em; color: #667eea; }
        table {
            width: 100%; border-collapse: collapse; margin: 20px 0; background: white;
            border-radius: 8px; overflow: hidden; box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #667eea; color: white; font-weight: 600; }
        tr:hover { background: #f5f5f5; }
        code {
            background: #f4f4f4; padding: 2px 6px; border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace; color: #e74c3c;
        }
        pre {
            background: #2c3e50; color: #ecf0f1; padding: 20px;
            border-radius: 5px; overflow-x: auto; margin: 20px 0;
        }
        blockquote {
            border-left: 4px solid #667eea; margin: 20px 0; padding: 15px 20px;
            background: #f8f9fa; font-style: italic;
        }
        #TOC { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px; }
        #TOC h2 { color: #667eea; margin-bottom: 15px; }
        .footer {
            background: #2c3e50; color: white; padding: 20px;
            text-align: center; font-size: 0.9em;
        }
        @media (max-width: 768px) {
            body { padding: 10px; } .content { padding: 20px; }
            .header { padding: 20px; } .header h1 { font-size: 2em; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>$if(title)$$title$$else$Document$endif$</h1>
            <div class="meta">Generated on $date$</div>
        </div>
        <div class="content">
            $if(toc)$<nav id="TOC"><h2>📋 Table of Contents</h2>$toc$</nav>$endif$
            $body$
        </div>
        <div class="footer">
            <div>Generated with Pandoc • $(date '+%B %d, %Y at %I:%M %p')</div>
        </div>
    </div>
</body>
</html>
EOF
    echo "$template_file"
}

# Function to create Word reference document
create_word_reference() {
    if command -v python3 &> /dev/null && python3 -c "import docx" 2>/dev/null; then
        python3 -c "
from docx import Document
from docx.shared import Pt
doc = Document()
styles = doc.styles
styles['Heading 1'].font.size = Pt(24)
styles['Heading 2'].font.size = Pt(20)
styles['Heading 3'].font.size = Pt(16)
styles['Normal'].font.size = Pt(11)
styles['Normal'].font.name = 'Calibri'
doc.save('reference.docx')
print('✅ Created Word reference document')
" 2>/dev/null || echo -e "${YELLOW}⚠️  Could not create Word reference document${NC}"
    fi
}

# Function to open file in appropriate application
open_file() {
    local file="$1"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        open "$file"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        xdg-open "$file"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        start "$file"
    else
        echo -e "${YELLOW}Cannot auto-open on this system. File: $file${NC}"
    fi
}

# Function to convert single file
convert_file() {
    local input_file="$1"
    local output_file="$2"
    
    if [[ ! -f "$input_file" ]]; then
        echo -e "${RED}❌ File not found: $input_file${NC}"
        return 1
    fi
    
    # Build Pandoc command
    local cmd=("pandoc" "$input_file" "-o" "$output_file")
    
    # Add table of contents
    if [[ "$CREATE_TOC" == true ]]; then
        cmd+=("--toc" "--toc-depth=3")
    fi
    
    # Add format-specific options
    case $OUTPUT_FORMAT in
        html)
            local template_file
            if [[ -n "$TEMPLATE" ]]; then
                template_file="$TEMPLATE"
            else
                template_file=$(create_html_template)
            fi
            cmd+=("--template" "$template_file" "--standalone" "--highlight-style=$HIGHLIGHT_STYLE")
            ;;
        docx)
            if [[ -f "reference.docx" ]]; then
                cmd+=("--reference-doc=reference.docx")
            fi
            cmd+=("--highlight-style=$HIGHLIGHT_STYLE")
            ;;
        pdf)
            cmd+=("--pdf-engine=xelatex" "--highlight-style=$HIGHLIGHT_STYLE")
            ;;
        *)
            cmd+=("--highlight-style=$HIGHLIGHT_STYLE")
            ;;
    esac
    
    # Execute conversion
    if "${cmd[@]}" 2>/dev/null; then
        echo -e "${GREEN}✅ Converted: $input_file → $output_file${NC}"
        
        # Clean up temporary template
        if [[ "$OUTPUT_FORMAT" == "html" && -z "$TEMPLATE" ]]; then
            rm -f "pandoc_template_$$.html" 2>/dev/null
        fi
        
        # Open file if requested
        if [[ "$OPEN_BROWSER" == true ]]; then
            open_file "$output_file"
        fi
        
        return 0
    else
        echo -e "${RED}❌ Failed to convert: $input_file${NC}"
        return 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--format)
            OUTPUT_FORMAT="$2"
            shift 2
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -b|--batch)
            BATCH_MODE=true
            shift
            ;;
        -w|--open)
            OPEN_BROWSER=true
            shift
            ;;
        --no-toc)
            CREATE_TOC=false
            shift
            ;;
        --highlight)
            HIGHLIGHT_STYLE="$2"
            shift 2
            ;;
        --template)
            TEMPLATE="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        -*)
            echo -e "${RED}Unknown option: $1${NC}"
            usage
            exit 1
            ;;
        *)
            break
            ;;
    esac
done

# Check if files are provided
if [[ $# -eq 0 ]]; then
    echo -e "${RED}Error: No input files specified.${NC}"
    usage
    exit 1
fi

# Validate output format
case $OUTPUT_FORMAT in
    html|docx|pdf|odt|rtf) ;;
    *)
        echo -e "${RED}Error: Unsupported format '$OUTPUT_FORMAT'${NC}"
        echo "Supported formats: html, docx, pdf, odt, rtf"
        exit 1
        ;;
esac

# Check dependencies
check_dependencies

# Create output directory if specified
if [[ -n "$OUTPUT_DIR" ]]; then
    mkdir -p "$OUTPUT_DIR"
    echo -e "${BLUE}📁 Output directory: $OUTPUT_DIR${NC}"
fi

# Create Word reference document for docx format
if [[ "$OUTPUT_FORMAT" == "docx" && ! -f "reference.docx" ]]; then
    create_word_reference
fi

echo -e "${PURPLE}🔄 Converting to $OUTPUT_FORMAT format...${NC}"
echo ""

# Convert files
success_count=0
total_count=$#

for input_file in "$@"; do
    # Generate output filename
    if [[ -n "$OUTPUT_DIR" ]]; then
        output_file="$OUTPUT_DIR/$(basename "${input_file%.*}.$OUTPUT_FORMAT")"
    else
        output_file="${input_file%.*}.$OUTPUT_FORMAT"
    fi
    
    if convert_file "$input_file" "$output_file"; then
        ((success_count++))
    fi
done

echo ""
echo -e "${BLUE}📊 Conversion Summary: $success_count/$total_count files converted successfully${NC}"

if [[ $success_count -gt 0 ]]; then
    echo -e "${GREEN}🎉 Conversion completed!${NC}"
else
    echo -e "${RED}❌ No files were converted successfully.${NC}"
    exit 1
fi
