#!/usr/bin/env python3
"""
Pandoc-based Document Converter
Converts markdown files to HTML, Word (DOCX), PDF, and other formats using Pandoc.

Usage:
    python pandoc_converter.py input.md --format html
    python pandoc_converter.py input.md --format docx
    python pandoc_converter.py --batch --format html *.md
    python pandoc_converter.py --help
"""

import argparse
import os
import sys
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

def check_pandoc():
    """Check if Pandoc is installed."""
    if not shutil.which('pandoc'):
        print("❌ Error: Pandoc is not installed.")
        print("\nTo install Pandoc:")
        print("  macOS:   brew install pandoc")
        print("  Ubuntu:  sudo apt-get install pandoc")
        print("  Windows: Download from https://pandoc.org/installing.html")
        print("\nFor Word conversion, also install:")
        print("  pip install python-docx")
        return False
    return True

def get_pandoc_version():
    """Get Pandoc version."""
    try:
        result = subprocess.run(['pandoc', '--version'], capture_output=True, text=True)
        version_line = result.stdout.split('\n')[0]
        return version_line
    except:
        return "Unknown version"

def create_html_template():
    """Create custom HTML template for Pandoc."""
    template_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 40px;
        }
        
        h1, h2, h3, h4, h5, h6 {
            margin: 30px 0 15px 0;
            color: #2c3e50;
        }
        
        h1 { font-size: 2.2em; border-bottom: 3px solid #667eea; padding-bottom: 10px; }
        h2 { font-size: 1.8em; border-bottom: 2px solid #ddd; padding-bottom: 8px; }
        h3 { font-size: 1.5em; color: #667eea; }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background: #f5f5f5;
        }
        
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            color: #e74c3c;
        }
        
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 20px 0;
        }
        
        blockquote {
            border-left: 4px solid #667eea;
            margin: 20px 0;
            padding: 15px 20px;
            background: #f8f9fa;
            font-style: italic;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            body { padding: 10px; }
            .content { padding: 20px; }
            .header { padding: 20px; }
            .header h1 { font-size: 2em; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>$if(title)$$title$$else$Document$endif$</h1>
            <div class="meta">Generated on $date$</div>
        </div>
        <div class="content">
            $if(toc)$
            <nav id="TOC">
                <h2>📋 Table of Contents</h2>
                $toc$
            </nav>
            $endif$
            $body$
        </div>
        <div class="footer">
            <div>Generated with Pandoc • $date$</div>
        </div>
    </div>
</body>
</html>"""
    
    template_path = Path("pandoc_template.html")
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(template_content)
    return template_path

def convert_document(input_file, output_format, output_file=None, options=None):
    """Convert document using Pandoc."""
    if not os.path.exists(input_file):
        print(f"❌ Error: Input file '{input_file}' not found.")
        return False
    
    # Generate output filename if not provided
    if output_file is None:
        input_path = Path(input_file)
        if output_format == 'html':
            output_file = input_path.with_suffix('.html')
        elif output_format == 'docx':
            output_file = input_path.with_suffix('.docx')
        elif output_format == 'pdf':
            output_file = input_path.with_suffix('.pdf')
        else:
            output_file = input_path.with_suffix(f'.{output_format}')
    
    # Build Pandoc command
    cmd = ['pandoc', input_file, '-o', str(output_file)]
    
    # Add format-specific options
    if output_format == 'html':
        template_path = create_html_template()
        cmd.extend([
            '--template', str(template_path),
            '--toc',
            '--toc-depth=3',
            '--standalone',
            '--highlight-style=github',
            '--variable', f'date={datetime.now().strftime("%B %d, %Y at %I:%M %p")}'
        ])
    elif output_format == 'docx':
        cmd.extend([
            '--toc',
            '--toc-depth=3',
            '--highlight-style=github',
            '--reference-doc=reference.docx' if os.path.exists('reference.docx') else ''
        ])
        # Remove empty reference-doc option
        cmd = [arg for arg in cmd if arg]
    elif output_format == 'pdf':
        cmd.extend([
            '--toc',
            '--toc-depth=3',
            '--highlight-style=github',
            '--pdf-engine=xelatex'
        ])
    
    # Add custom options
    if options:
        cmd.extend(options)
    
    try:
        # Run Pandoc
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"✅ Converted: {input_file} → {output_file}")
        
        # Clean up template file for HTML
        if output_format == 'html' and 'template_path' in locals():
            try:
                os.remove(template_path)
            except:
                pass
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error converting {input_file}:")
        print(f"   Command: {' '.join(cmd)}")
        print(f"   Error: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error converting {input_file}: {str(e)}")
        return False

def create_word_reference_doc():
    """Create a reference Word document for styling."""
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.style import WD_STYLE_TYPE
        
        doc = Document()
        
        # Modify styles
        styles = doc.styles
        
        # Heading styles
        heading1 = styles['Heading 1']
        heading1.font.size = Pt(24)
        heading1.font.color.rgb = None  # Use theme color
        
        heading2 = styles['Heading 2']
        heading2.font.size = Pt(20)
        
        heading3 = styles['Heading 3']
        heading3.font.size = Pt(16)
        
        # Normal style
        normal = styles['Normal']
        normal.font.size = Pt(11)
        normal.font.name = 'Calibri'
        
        # Save reference document
        doc.save('reference.docx')
        print("✅ Created Word reference document (reference.docx)")
        return True
        
    except ImportError:
        print("⚠️  python-docx not installed. Word documents will use default styling.")
        print("   Install with: pip install python-docx")
        return False
    except Exception as e:
        print(f"⚠️  Could not create Word reference document: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description="Convert Markdown files using Pandoc",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python pandoc_converter.py report.md --format html
  python pandoc_converter.py report.md --format docx
  python pandoc_converter.py report.md --format pdf
  python pandoc_converter.py --batch --format html *.md
  python pandoc_converter.py --batch --format docx --output-dir word_docs *.md
        """
    )
    
    parser.add_argument('input', nargs='*', help='Input markdown file(s)')
    parser.add_argument('-f', '--format', choices=['html', 'docx', 'pdf', 'odt', 'rtf'], 
                       default='html', help='Output format (default: html)')
    parser.add_argument('-o', '--output', help='Output file (for single file conversion)')
    parser.add_argument('--batch', action='store_true', help='Batch convert multiple files')
    parser.add_argument('--output-dir', help='Output directory for batch conversion')
    parser.add_argument('--pandoc-options', nargs='*', help='Additional Pandoc options')
    parser.add_argument('--create-reference', action='store_true', 
                       help='Create Word reference document for styling')
    
    args = parser.parse_args()
    
    # Check Pandoc installation
    if not check_pandoc():
        sys.exit(1)
    
    print(f"🔧 Using {get_pandoc_version()}")
    
    # Create Word reference document if requested
    if args.create_reference:
        create_word_reference_doc()
        if not args.input:
            return
    
    if not args.input:
        parser.print_help()
        return
    
    # Create output directory if specified
    if args.output_dir:
        os.makedirs(args.output_dir, exist_ok=True)
    
    # Create Word reference document for docx format
    if args.format == 'docx' and not os.path.exists('reference.docx'):
        create_word_reference_doc()
    
    success_count = 0
    total_count = len(args.input)
    
    for input_file in args.input:
        if not os.path.exists(input_file):
            print(f"❌ File not found: {input_file}")
            continue
        
        if args.batch or len(args.input) > 1:
            # Batch mode or multiple files
            if args.output_dir:
                output_file = os.path.join(
                    args.output_dir, 
                    Path(input_file).with_suffix(f'.{args.format}').name
                )
            else:
                output_file = Path(input_file).with_suffix(f'.{args.format}')
        else:
            # Single file mode
            output_file = args.output
        
        if convert_document(input_file, args.format, output_file, args.pandoc_options):
            success_count += 1
    
    print(f"\n📊 Conversion Summary: {success_count}/{total_count} files converted successfully")

if __name__ == "__main__":
    main()
