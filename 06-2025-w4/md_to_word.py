#!/usr/bin/env python3
"""
Markdown to Word Converter
Specialized script for converting Markdown to professional Word documents.

Usage:
    python md_to_word.py input.md [output.docx]
    python md_to_word.py --batch *.md
    python md_to_word.py --help
"""

import argparse
import os
import sys
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

def check_dependencies():
    """Check if required tools are installed."""
    missing = []
    
    if not shutil.which('pandoc'):
        missing.append("pandoc")
    
    try:
        import docx
    except ImportError:
        missing.append("python-docx")
    
    if missing:
        print("❌ Missing dependencies:")
        for dep in missing:
            print(f"   - {dep}")
        print("\nTo install:")
        if "pandoc" in missing:
            print("  Pandoc: brew install pandoc (macOS) or sudo apt-get install pandoc (Ubuntu)")
        if "python-docx" in missing:
            print("  python-docx: pip install python-docx")
        return False
    
    return True

def create_word_reference_document():
    """Create a professional Word reference document for styling."""
    try:
        from docx import Document
        from docx.shared import Inches, Pt, RGBColor
        from docx.enum.style import WD_STYLE_TYPE
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        
        doc = Document()
        
        # Get styles
        styles = doc.styles
        
        # Configure Normal style
        normal_style = styles['Normal']
        normal_font = normal_style.font
        normal_font.name = 'Calibri'
        normal_font.size = Pt(11)
        normal_style.paragraph_format.space_after = Pt(6)
        normal_style.paragraph_format.line_spacing = 1.15
        
        # Configure Heading 1
        heading1_style = styles['Heading 1']
        heading1_font = heading1_style.font
        heading1_font.name = 'Calibri Light'
        heading1_font.size = Pt(24)
        heading1_font.color.rgb = RGBColor(47, 84, 150)  # Professional blue
        heading1_style.paragraph_format.space_before = Pt(12)
        heading1_style.paragraph_format.space_after = Pt(12)
        
        # Configure Heading 2
        heading2_style = styles['Heading 2']
        heading2_font = heading2_style.font
        heading2_font.name = 'Calibri Light'
        heading2_font.size = Pt(18)
        heading2_font.color.rgb = RGBColor(68, 114, 196)
        heading2_style.paragraph_format.space_before = Pt(10)
        heading2_style.paragraph_format.space_after = Pt(6)
        
        # Configure Heading 3
        heading3_style = styles['Heading 3']
        heading3_font = heading3_style.font
        heading3_font.name = 'Calibri'
        heading3_font.size = Pt(14)
        heading3_font.color.rgb = RGBColor(68, 114, 196)
        heading3_font.bold = True
        heading3_style.paragraph_format.space_before = Pt(8)
        heading3_style.paragraph_format.space_after = Pt(4)
        
        # Configure Heading 4
        heading4_style = styles['Heading 4']
        heading4_font = heading4_style.font
        heading4_font.name = 'Calibri'
        heading4_font.size = Pt(12)
        heading4_font.bold = True
        heading4_style.paragraph_format.space_before = Pt(6)
        heading4_style.paragraph_format.space_after = Pt(3)
        
        # Create a custom table style
        try:
            table_style = doc.styles.add_style('Professional Table', WD_STYLE_TYPE.TABLE)
            table_style.base_style = styles['Table Grid']
        except:
            pass  # Style might already exist
        
        # Set document margins
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            section.left_margin = Inches(1)
            section.right_margin = Inches(1)
        
        # Add a sample paragraph to establish the document structure
        doc.add_paragraph("This is a reference document for styling. It will be used as a template for converting Markdown to Word documents with professional formatting.")
        
        # Save the reference document
        doc.save('word_reference.docx')
        print("✅ Created Word reference document (word_reference.docx)")
        return True
        
    except Exception as e:
        print(f"⚠️  Could not create Word reference document: {str(e)}")
        return False

def convert_to_word(input_file, output_file=None, use_toc=True):
    """Convert markdown file to Word document using Pandoc."""
    if not os.path.exists(input_file):
        print(f"❌ Error: Input file '{input_file}' not found.")
        return False
    
    # Generate output filename if not provided
    if output_file is None:
        output_file = Path(input_file).with_suffix('.docx')
    
    # Ensure reference document exists
    if not os.path.exists('word_reference.docx'):
        create_word_reference_document()
    
    # Build Pandoc command
    cmd = [
        'pandoc',
        input_file,
        '-o', str(output_file),
        '--from', 'markdown',
        '--to', 'docx'
    ]
    
    # Add table of contents
    if use_toc:
        cmd.extend(['--toc', '--toc-depth=3'])
    
    # Add reference document for styling
    if os.path.exists('word_reference.docx'):
        cmd.extend(['--reference-doc=word_reference.docx'])
    
    # Add other options for better formatting
    cmd.extend([
        '--highlight-style=github',
        '--standalone'
    ])
    
    try:
        # Run Pandoc
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"✅ Converted: {input_file} → {output_file}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error converting {input_file}:")
        print(f"   Command: {' '.join(cmd)}")
        if e.stderr:
            print(f"   Error: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error converting {input_file}: {str(e)}")
        return False

def enhance_word_document(docx_file):
    """Post-process the Word document to add professional touches."""
    try:
        from docx import Document
        from docx.shared import Pt, RGBColor
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        
        doc = Document(docx_file)
        
        # Add header with document title and date
        section = doc.sections[0]
        header = section.header
        header_para = header.paragraphs[0]
        header_para.text = f"Generated on {datetime.now().strftime('%B %d, %Y')}"
        header_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        header_para.style.font.size = Pt(9)
        header_para.style.font.color.rgb = RGBColor(128, 128, 128)
        
        # Add footer with page numbers
        footer = section.footer
        footer_para = footer.paragraphs[0]
        footer_para.text = "Page "
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        footer_para.style.font.size = Pt(9)
        
        # Enhance tables
        for table in doc.tables:
            # Set table style
            table.style = 'Table Grid'
            
            # Style header row
            if len(table.rows) > 0:
                header_row = table.rows[0]
                for cell in header_row.cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.bold = True
                            run.font.color.rgb = RGBColor(255, 255, 255)
                    # Set cell background (this requires more complex manipulation)
        
        # Save the enhanced document
        doc.save(docx_file)
        print(f"✨ Enhanced Word document: {docx_file}")
        return True
        
    except Exception as e:
        print(f"⚠️  Could not enhance Word document: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description="Convert Markdown files to professional Word documents",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python md_to_word.py report.md
  python md_to_word.py report.md custom_output.docx
  python md_to_word.py --batch *.md
  python md_to_word.py --batch --output-dir word_docs *.md
  python md_to_word.py --no-toc report.md
        """
    )
    
    parser.add_argument('input', nargs='*', help='Input markdown file(s)')
    parser.add_argument('-o', '--output', help='Output Word file (for single file conversion)')
    parser.add_argument('--batch', action='store_true', help='Batch convert multiple files')
    parser.add_argument('--output-dir', help='Output directory for batch conversion')
    parser.add_argument('--no-toc', action='store_true', help='Disable table of contents')
    parser.add_argument('--no-enhance', action='store_true', help='Skip document enhancement')
    parser.add_argument('--create-reference', action='store_true', 
                       help='Create Word reference document and exit')
    
    args = parser.parse_args()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Create reference document if requested
    if args.create_reference:
        create_word_reference_document()
        return
    
    if not args.input:
        parser.print_help()
        return
    
    # Create output directory if specified
    if args.output_dir:
        os.makedirs(args.output_dir, exist_ok=True)
    
    # Ensure reference document exists
    if not os.path.exists('word_reference.docx'):
        print("📝 Creating Word reference document for professional styling...")
        create_word_reference_document()
    
    success_count = 0
    total_count = len(args.input)
    use_toc = not args.no_toc
    
    print(f"🔄 Converting {total_count} file(s) to Word format...")
    print("")
    
    for input_file in args.input:
        if not os.path.exists(input_file):
            print(f"❌ File not found: {input_file}")
            continue
        
        if args.batch or len(args.input) > 1:
            # Batch mode or multiple files
            if args.output_dir:
                output_file = os.path.join(
                    args.output_dir, 
                    Path(input_file).with_suffix('.docx').name
                )
            else:
                output_file = Path(input_file).with_suffix('.docx')
        else:
            # Single file mode
            output_file = args.output
        
        if convert_to_word(input_file, output_file, use_toc):
            success_count += 1
            
            # Enhance the document unless disabled
            if not args.no_enhance:
                enhance_word_document(output_file)
    
    print("")
    print(f"📊 Conversion Summary: {success_count}/{total_count} files converted successfully")
    
    if success_count > 0:
        print("🎉 Word conversion completed!")
        print("")
        print("💡 Tips:")
        print("   - Open the Word documents to review formatting")
        print("   - Customize word_reference.docx to change default styling")
        print("   - Use --no-enhance to skip post-processing")
    else:
        print("❌ No files were converted successfully.")

if __name__ == "__main__":
    main()
