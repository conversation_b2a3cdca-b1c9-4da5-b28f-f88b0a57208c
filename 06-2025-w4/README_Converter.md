# Markdown to HTML Converter

A powerful Python script that converts Markdown files to beautifully styled HTML documents with professional formatting, responsive design, and enhanced features.

## Features

✨ **Professional Styling**
- Modern, responsive design that works on all devices
- Beautiful gradient headers and clean typography
- Professional color scheme and spacing

📋 **Automatic Table of Contents**
- Generates clickable TOC from headers
- Smooth navigation within documents
- Collapsible sections for better organization

📊 **Enhanced Tables**
- Styled tables with hover effects
- Responsive table design
- Professional borders and spacing

🎨 **Status Badge Support**
- Automatically converts emoji status indicators to styled badges
- Supports: ✅ Completed, 🔄 In Progress, 📅 Scheduled, 🔴 High Risk, ⏸️ On Hold

💻 **Code Highlighting**
- Syntax highlighting for code blocks
- Inline code styling
- Dark theme for code blocks

📱 **Responsive Design**
- Mobile-friendly layout
- Print-optimized styling
- Cross-browser compatibility

## Installation

### Prerequisites
- Python 3.6 or higher
- pip (Python package installer)

### Install Dependencies
```bash
pip install -r requirements.txt
```

Or install manually:
```bash
pip install markdown
```

## Usage

### Basic Usage

**Convert single file:**
```bash
python markdown_to_html.py report.md
```

**Convert with custom output:**
```bash
python markdown_to_html.py report.md output.html
```

**Batch convert multiple files:**
```bash
python markdown_to_html.py --batch *.md
```

**Batch convert to specific directory:**
```bash
python markdown_to_html.py --batch --output-dir html_files *.md
```

### Using the Shell Script (Linux/macOS)

Make the script executable:
```bash
chmod +x convert_md.sh
```

**Basic conversion:**
```bash
./convert_md.sh report.md
```

**Batch convert with browser opening:**
```bash
./convert_md.sh -b -w *.md
```

**Convert to specific directory:**
```bash
./convert_md.sh -o html_output *.md
```

### Command Line Options

#### Python Script Options:
- `input`: Input markdown file(s)
- `-o, --output`: Output HTML file (for single file conversion)
- `--batch`: Enable batch conversion mode
- `--output-dir`: Output directory for batch conversion

#### Shell Script Options:
- `-o, --output-dir DIR`: Output directory for HTML files
- `-b, --batch`: Enable batch mode
- `-w, --open`: Open generated HTML in browser
- `-h, --help`: Show help message

## Examples

### Convert Your Project Reports
```bash
# Convert all your weekly reports
python markdown_to_html.py --batch --output-dir reports_html *.md

# Convert specific files
python markdown_to_html.py PMC_SmartPay_Update_20250626.md Yuda_Weekly_Report_W3_Jun_20.md
```

### Using with Your Current Files
```bash
# Convert the summary report
python markdown_to_html.py Weekly_Summary_Report_June_2025.md

# Convert all markdown files in current directory
./convert_md.sh -b -w *.md
```

## Output Features

The generated HTML includes:

### 🎨 **Visual Elements**
- Professional header with gradient background
- Clean, readable typography
- Responsive layout for all screen sizes
- Print-friendly styling

### 📋 **Content Organization**
- Automatic table of contents generation
- Clickable navigation links
- Hierarchical header styling
- Status badge conversion

### 📊 **Table Enhancement**
- Styled tables with hover effects
- Responsive table design
- Professional appearance

### 💻 **Code Display**
- Syntax highlighting
- Dark theme code blocks
- Inline code styling

## Supported Markdown Extensions

- **Tables**: Full table support with styling
- **Fenced Code Blocks**: Syntax highlighting
- **Table of Contents**: Auto-generated from headers
- **Attributes**: Custom attributes for elements
- **Definition Lists**: Enhanced list formatting
- **Abbreviations**: Expandable abbreviations
- **Footnotes**: Reference-style footnotes

## Customization

The HTML template includes comprehensive CSS that can be customized by modifying the `get_html_template()` function in `markdown_to_html.py`.

### Key CSS Classes:
- `.container`: Main content wrapper
- `.header`: Document header section
- `.content`: Main content area
- `.toc`: Table of contents styling
- `.status-badge`: Status indicator badges
- `.footer`: Document footer

## Browser Compatibility

- ✅ Chrome/Chromium (all versions)
- ✅ Firefox (all versions)
- ✅ Safari (all versions)
- ✅ Edge (all versions)
- ✅ Mobile browsers

## Print Support

The generated HTML includes print-optimized CSS for professional document printing:
- Optimized page breaks
- Print-specific styling
- Clean black and white output

## Troubleshooting

### Common Issues:

**"markdown module not found"**
```bash
pip install markdown
```

**Permission denied (shell script)**
```bash
chmod +x convert_md.sh
```

**Python not found**
- Ensure Python 3.6+ is installed
- Try using `python3` instead of `python`

## License

This script is provided as-is for project use. Feel free to modify and distribute as needed.

## Contributing

To improve the converter:
1. Modify the CSS in `get_html_template()`
2. Add new markdown extensions in the `convert_markdown_to_html()` function
3. Enhance the status badge patterns in `enhance_content()`

---

*Happy converting! 🚀*
