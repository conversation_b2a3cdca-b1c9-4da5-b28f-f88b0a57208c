#!/usr/bin/env python3
"""
Markdown to HTML Converter Script
Converts markdown files to styled HTML with table of contents and responsive design.

Usage:
    python markdown_to_html.py input.md [output.html]
    python markdown_to_html.py --batch *.md
    python markdown_to_html.py --help
"""

import argparse
import os
import sys
import re
from pathlib import Path
from datetime import datetime

try:
    import markdown
    from markdown.extensions import toc, tables, codehilite, fenced_code
except ImportError:
    print("Error: Required packages not installed.")
    print("Please install: pip install markdown")
    sys.exit(1)

def get_html_template():
    """Return the HTML template with CSS styling."""
    return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
        }}
        
        .header .meta {{
            opacity: 0.9;
            font-size: 1.1em;
        }}
        
        .content {{
            padding: 40px;
        }}
        
        .toc {{
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }}
        
        .toc h2 {{
            color: #667eea;
            margin-bottom: 15px;
        }}
        
        .toc ul {{
            list-style: none;
        }}
        
        .toc li {{
            margin: 8px 0;
        }}
        
        .toc a {{
            color: #555;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 3px;
            transition: all 0.3s;
        }}
        
        .toc a:hover {{
            background: #667eea;
            color: white;
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            margin: 30px 0 15px 0;
            color: #2c3e50;
        }}
        
        h1 {{ font-size: 2.2em; border-bottom: 3px solid #667eea; padding-bottom: 10px; }}
        h2 {{ font-size: 1.8em; border-bottom: 2px solid #ddd; padding-bottom: 8px; }}
        h3 {{ font-size: 1.5em; color: #667eea; }}
        h4 {{ font-size: 1.3em; }}
        
        p {{
            margin: 15px 0;
            text-align: justify;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        
        th, td {{
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        
        th {{
            background: #667eea;
            color: white;
            font-weight: 600;
        }}
        
        tr:hover {{
            background: #f5f5f5;
        }}
        
        code {{
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            color: #e74c3c;
        }}
        
        pre {{
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 20px 0;
        }}
        
        pre code {{
            background: none;
            color: inherit;
            padding: 0;
        }}
        
        blockquote {{
            border-left: 4px solid #667eea;
            margin: 20px 0;
            padding: 15px 20px;
            background: #f8f9fa;
            font-style: italic;
        }}
        
        ul, ol {{
            margin: 15px 0;
            padding-left: 30px;
        }}
        
        li {{
            margin: 8px 0;
        }}
        
        .status-badge {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin: 2px;
        }}
        
        .status-completed {{ background: #d4edda; color: #155724; }}
        .status-progress {{ background: #fff3cd; color: #856404; }}
        .status-scheduled {{ background: #cce5ff; color: #004085; }}
        .status-risk {{ background: #f8d7da; color: #721c24; }}
        
        .footer {{
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 0.9em;
        }}
        
        @media (max-width: 768px) {{
            body {{ padding: 10px; }}
            .content {{ padding: 20px; }}
            .header {{ padding: 20px; }}
            .header h1 {{ font-size: 2em; }}
            table {{ font-size: 0.9em; }}
        }}
        
        .print-only {{ display: none; }}
        
        @media print {{
            body {{ background: white; padding: 0; }}
            .container {{ box-shadow: none; }}
            .header {{ background: #667eea !important; }}
            .print-only {{ display: block; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{title}</h1>
            <div class="meta">Generated on {date}</div>
        </div>
        <div class="content">
            {content}
        </div>
        <div class="footer">
            <div class="print-only">Document generated from Markdown</div>
            <div>Converted from Markdown to HTML • {filename}</div>
        </div>
    </div>
</body>
</html>"""

def extract_title_from_markdown(content):
    """Extract title from markdown content."""
    lines = content.split('\n')
    for line in lines:
        line = line.strip()
        if line.startswith('# '):
            return line[2:].strip()
    return "Document"

def enhance_content(html_content):
    """Enhance HTML content with status badges and styling."""
    # Add status badges for common patterns
    patterns = [
        (r'✅\s*([^<\n]+)', r'<span class="status-badge status-completed">✅ \1</span>'),
        (r'🔄\s*([^<\n]+)', r'<span class="status-badge status-progress">🔄 \1</span>'),
        (r'📅\s*([^<\n]+)', r'<span class="status-badge status-scheduled">📅 \1</span>'),
        (r'🔴\s*([^<\n]+)', r'<span class="status-badge status-risk">🔴 \1</span>'),
        (r'⏸️\s*([^<\n]+)', r'<span class="status-badge status-progress">⏸️ \1</span>'),
    ]
    
    for pattern, replacement in patterns:
        html_content = re.sub(pattern, replacement, html_content)
    
    return html_content

def convert_markdown_to_html(input_file, output_file=None):
    """Convert a single markdown file to HTML."""
    try:
        # Read markdown file
        with open(input_file, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
        
        # Extract title
        title = extract_title_from_markdown(markdown_content)
        
        # Configure markdown extensions
        md = markdown.Markdown(extensions=[
            'toc',
            'tables',
            'fenced_code',
            'codehilite',
            'attr_list',
            'def_list',
            'abbr',
            'footnotes'
        ])
        
        # Convert to HTML
        html_content = md.convert(markdown_content)
        
        # Enhance content
        html_content = enhance_content(html_content)
        
        # Add table of contents if headers exist
        if md.toc:
            toc_html = f'<div class="toc"><h2>📋 Table of Contents</h2>{md.toc}</div>'
            html_content = toc_html + html_content
        
        # Generate output filename if not provided
        if output_file is None:
            output_file = Path(input_file).with_suffix('.html')
        
        # Create final HTML
        final_html = get_html_template().format(
            title=title,
            content=html_content,
            date=datetime.now().strftime("%B %d, %Y at %I:%M %p"),
            filename=Path(input_file).name
        )
        
        # Write HTML file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_html)
        
        print(f"✅ Converted: {input_file} → {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error converting {input_file}: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description="Convert Markdown files to styled HTML",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python markdown_to_html.py report.md
  python markdown_to_html.py report.md output.html
  python markdown_to_html.py --batch *.md
  python markdown_to_html.py --batch --output-dir html_files *.md
        """
    )
    
    parser.add_argument('input', nargs='*', help='Input markdown file(s)')
    parser.add_argument('-o', '--output', help='Output HTML file (for single file conversion)')
    parser.add_argument('--batch', action='store_true', help='Batch convert multiple files')
    parser.add_argument('--output-dir', help='Output directory for batch conversion')
    
    args = parser.parse_args()
    
    if not args.input:
        parser.print_help()
        return
    
    # Create output directory if specified
    if args.output_dir:
        os.makedirs(args.output_dir, exist_ok=True)
    
    success_count = 0
    total_count = len(args.input)
    
    for input_file in args.input:
        if not os.path.exists(input_file):
            print(f"❌ File not found: {input_file}")
            continue
        
        if args.batch or len(args.input) > 1:
            # Batch mode or multiple files
            if args.output_dir:
                output_file = os.path.join(args.output_dir, Path(input_file).with_suffix('.html').name)
            else:
                output_file = Path(input_file).with_suffix('.html')
        else:
            # Single file mode
            output_file = args.output
        
        if convert_markdown_to_html(input_file, output_file):
            success_count += 1
    
    print(f"\n📊 Conversion Summary: {success_count}/{total_count} files converted successfully")

if __name__ == "__main__":
    main()
